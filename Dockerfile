FROM ubuntu:24.04 AS python-base
RUN apt-get update
RUN DEBIAN_FRONTEND=noninteractive apt-get install -y software-properties-common && \
    add-apt-repository -y ppa:deadsnakes/ppa && \
    apt-get update && \
    apt-get install -y python3.9
RUN apt-get update

# Install Puppeteer dependencies
RUN apt-get update && apt-get install -y \
    libgtk2.0-0t64 libgtk-3-0t64 libgbm-dev libnotify-dev libnss3 libxss1 libasound2t64 libxtst6 xauth xvfb
# Install Aptos CLI
RUN apt-get update && apt-get install -y curl && apt-get install -y vim-common &&\
    curl -fsSL "https://aptos.dev/scripts/install_cli.py" | python3

# Debug: Run Aptos CLI directly
RUN /root/.local/bin/aptos --version
ENV PATH="/root/.local/bin:$PATH"

FROM node:20 AS app-base

# Copy Aptos CLI binaries from python-base
COPY --from=python-base /root/.local/bin /root/.local/bin
COPY --from=python-base /usr/bin/xxd /usr/bin/xxd

# Copy library directories wholesale for Puppeteer dependencies
COPY --from=python-base /usr/lib/x86_64-linux-gnu /usr/lib/x86_64-linux-gnu
COPY --from=python-base /lib/x86_64-linux-gnu /lib/x86_64-linux-gnu

ENV PATH="/root/.local/bin:$PATH"
WORKDIR /
COPY package*.json ./
COPY ./ ./

# Debug: Verify Aptos CLI availability
RUN aptos --version || echo "aptos command failed"

# Install global npm packages
RUN npm install -g npm@10.9.0 tsx

# Drizzle and other package installations
RUN npm install tsx
RUN npm install -D drizzle-kit
RUN npx drizzle-kit generate --config=drizzle.config.ts
RUN npm run generate-migration
RUN npm run apply-migration
# Build the application
RUN npm run build
# Expose the application port
EXPOSE 8081
# Start the application
CMD [ "npm", "run", "start" ]

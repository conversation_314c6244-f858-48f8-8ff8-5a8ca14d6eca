{"name": "<PERSON><PERSON>", "version": "1.0.0", "main": "index.js", "license": "MIT", "engines": {"node": ">=20.0.0"}, "scripts": {"start": "node -r module-alias/register dist/server.js", "build": "tsc", "dev": "nodemon --watch src --ext ts --exec 'node --inspect -r ts-node/register -r tsconfig-paths/register' src/server.ts", "generate-migration": "drizzle-kit generate", "apply-migration": "drizzle-kit migrate", "studio": "drizzle-kit studio", "clean": "rm -rf dist", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "check-types": "tsc --noEmit", "prepare": "npm run build", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@aptos-labs/ts-sdk": "1.38.0", "@merkletrade/ts-sdk": "^1.0.3", "@panoraexchange/swap-sdk": "^1.0.8", "axios": "^1.7.7", "cheerio": "^1.0.0", "cors": "^2.8.5", "dotenv": "^16.4.5", "drizzle-orm": "^0.36.3", "ethjs-util": "^0.1.6", "express": "^4.21.1", "jsonwebtoken": "^9.0.2", "module-alias": "^2.2.3", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "openai": "^4.77.3", "pg": "^8.13.1", "technicalindicators": "^3.1.0", "tsconfig-paths": "^4.2.0", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1", "uuid": "^11.0.3", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/ethjs-util": "^0.1.3", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.9.1", "@types/node-cron": "^3.0.11", "@types/pg": "^8.11.10", "drizzle-kit": "^0.28.1", "jest": "^29.7.0", "nodemon": "^3.1.7", "prettier": "^3.5.3", "ts-jest": "^29.3.1", "ts-node": "^10.9.2", "typescript": "^5.6.3"}, "resolutions": {"@aptos-labs/ts-sdk": "^1.33.2"}, "_moduleAliases": {"@": "dist"}}
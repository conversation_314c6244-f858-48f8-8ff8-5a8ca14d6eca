{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "node",
      "request": "attach",
      "name": "Attach to Nodemon",
      "restart": true,
      "port": 9229, // Default port for --inspect
      "cwd": "${workspaceFolder}",
      "runtimeExecutable": "nodemon",
      "args": [
        "--watch",
        "src",
        "--ext",
        "ts",
        "--exec",
        "node --inspect -r ts-node/register -r tsconfig-paths/register"
      ],
      "skipFiles": ["<node_internals>/**"]
    }
  ]
}

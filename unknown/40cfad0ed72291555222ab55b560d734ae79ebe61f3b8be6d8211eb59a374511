import { Request, Response } from 'express';
import * as agentService from '@/services/agent';
import { ISafeAgent } from '@/db/schemas';

export const getAgent = async (req: Request, res: Response): Promise<void> => {
  try {
    const agentId = req.params.agentId;
    const agent: ISafeAgent | null = await agentService.getAgentById(Number(agentId));

    if (!agent) {
      res.status(404).json({ message: 'Agent not found' });
      return;
    }

    res.status(200).json(agent);
    return;
  } catch (error) {
    console.error('Error in getAgent:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export const getAgentByUserId = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }
    const agent = await agentService.getAgentByUserId(userId);

    if (!agent) {
      res.status(404).json({ message: 'Agent not found' });
      return;
    }

    res.status(200).json(agent);
    return;
  } catch (error) {
    console.error('Error in getAgentByUserId:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

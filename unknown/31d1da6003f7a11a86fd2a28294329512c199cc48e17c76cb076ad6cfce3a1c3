import { Request, Response } from 'express';
import * as agentService from '@/services/agent';
import * as userService from '@/services/user';
import decryptPrivateKey from '@/utils/blockchain/cryptography/decryptPrivateKey';

export const exportPrivateKey = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const user = (await userService.getUserById(userId)) as any;

    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }

    const agent = await agentService.getAgentDetail(user.agent.id);
    const privateKey = agent.privateKey;

    if (!privateKey) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const decryptedPrivateKey = decryptPrivate<PERSON>ey(privateKey);

    res.status(200).json({ decryptedPrivateKey });
    return;
  } catch (error) {
    console.error('Error in getAgentByUserId:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

import { Request, Response } from 'express';
import * as userService from '@/services/user';
import * as agentService from '@/services/agent';
import {
  fetchOpenSnipeRecords,
  getCurrentPriceOfToken,
  insertSnipeOrder,
  setSnipeOrderClosed,
  SnipeOrderConfig,
} from '@/db/indexer/snipe';
const { NETWORK } = process.env;

export const placeSnipeOrder = async (req: Request, res: Response): Promise<void> => {
  try {
    // if (NETWORK !== "mainnet") {
    //   res.status(400).json({ error: "Snipe orders are only available on mainnet" });
    //   return;
    // }
    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }

    if(user.remainingPrompts < 1) {
      res.status(400).json({ error: 'Insufficient prompts' });
      return;
    }

    const agent = await agentService.getAgentDetail(user.agent.id);
    if (!agent) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const address = agent.publicKey?.toString();
    if (!address) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const priceDetails = await getCurrentPriceOfToken(req.body.target_token_address);
    if (!priceDetails) {
      res.status(400).json({
        error: `Failed to fetch price for target token: ${req.body.target_token_address}`,
      });
      return;
    }
    const snipeOrder: SnipeOrderConfig = {
      target_token_address: req.body.target_token_address,
      base_token_address: req.body.base_token_address,
      reference_price: priceDetails?.rate,
      price_change_threshold: req.body.price_change_threshold,
      direction: req.body.direction,
      buy_amount: req.body.buy_amount,
      polling_interval: req.body.polling_interval,
      slippage_tolerance: req.body.slippage_tolerance,
      take_profit_percent: req.body.take_profit_percent,
      stop_loss_percent: req.body.stop_loss_percent,
      user_wallet_address: user.address,
      agent_wallet_address: address,
    };
    const { rowCount } = await insertSnipeOrder(snipeOrder);

    await userService.updateRemainingPrompts(user.id, user.remainingPrompts - 1);

    res.status(200).json({ message: 'Order placed successfully', data: { orderPlaced: rowCount } });
    return;
  } catch (error) {
    console.error('Error in placeSnipeOrder:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export const closeSnipeOrder = async (req: Request, res: Response): Promise<void> => {
  try {
    const { id } = req.params;
    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    if (!agent) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const address = agent.publicKey?.toString();
    if (!address) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    await setSnipeOrderClosed(Number(id), address);
    res.status(200).json({ message: 'Order closed successfully' });
    return;
  } catch (error) {
    console.error('Error in closeSnipeOrder:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export const getOpenSnipeOrders = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token_address } = req.body;

    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    const agent_address = agent.publicKey?.toString();
    if (!agent_address) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const result = await fetchOpenSnipeRecords(agent_address, token_address);

    res.status(200).json({ message: 'Orders fetched.', data: { orders: result } });
    return;
  } catch (error) {
    console.error('Error in getting dca orders:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

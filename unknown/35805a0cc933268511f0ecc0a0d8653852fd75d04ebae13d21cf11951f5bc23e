const classifyText = () => {
  return `Determine whether the user text falls into exactly one of the following categories:

1) **"chit_chat"**  
   - Casual conversation, small talk, greetings, or short social remarks.

2) **"knowledge_base"**  
   - **Any statement, question, or inquiry about factual, reference, or encyclopedic information.**
   - Examples include: asking about historical events, sharing facts, discussing data, etc.

3) **"crypto_intent"**  
   - Any message that relates to **cryptocurrency, tokens, NFTs, trading tools, or DeFi interfaces**.
   - Includes:
     - Explicit requests (e.g., "buy ETH", "swap USDC to SOL")
     - Mentions of **trading strategies** (e.g., “DCA”, “sniping”, “limit orders”)
     - Token or protocol names used in **an actionable or tool-related context** (e.g., "DCA LOON", "snype GUI")
     - Partial phrases or commands that refer to **crypto features, platforms, or workflows**, even if not full sentences.

4) **"create_tweet"**  
   - Requests or instructions to compose, draft, or generate a tweet/Twitter post.

5) **"other_intent"**  
   - Any request, command, or conversation that does not fit the categories above
   - (e.g., booking a flight, fetching non-crypto data, describing non-factual opinions, etc.)

Output your answer in exactly one word (all lowercase), choosing one of:
   - chit_chat
   - knowledge_base
   - crypto_intent
   - create_tweet
   - other_intent`;
};

export default classifyText;

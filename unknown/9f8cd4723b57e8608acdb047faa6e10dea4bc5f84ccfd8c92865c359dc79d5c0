import OpenAI from 'openai';
import BaseLLMClient from './baseClient';
import * as dotenv from 'dotenv';

dotenv.config();

export class GPTClient extends BaseLLMClient {
  private openai: OpenAI;

  constructor() {
    super();
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('Missing required environment variable: OPENAI_API_KEY');
    }
    this.openai = new OpenAI({ apiKey });
  }

  public getModelName(): string {
    // return "gpt-4";
    return 'gpt-4o-mini';
  }

  public setModelName(modelName: string): void {
    this.modelName = modelName;
  }

  protected async makeApiCall(payload: any): Promise<string | void> {
    try {
      const response = await this.openai.chat.completions.create(payload);

      const data = response.choices[0]?.message?.content?.trim();
      if (!data) {
        throw new Error('Unexpected response format.');
      }
      return data;
    } catch (err) {
      console.error('Error in GPT API call:', err);
      throw new Error('Failed to fetch response from GPT API.');
    }
  }
}

export default GPTClient;

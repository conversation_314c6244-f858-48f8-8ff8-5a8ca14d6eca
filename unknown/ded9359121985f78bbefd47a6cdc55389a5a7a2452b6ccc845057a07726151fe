import { Response, Request } from 'express';
import * as userService from '@/services/user';

const getUser = async (req: Request, res: Response) => {
  try {
    if (!req.user) {
      res.status(400).json({ message: 'User not found' });
      return;
    }

    const user = await userService.getUserById(req.user.id);
    res.json(user);
  } catch (error) {
    console.error('Error in getUser:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
  }
};

export default getUser;

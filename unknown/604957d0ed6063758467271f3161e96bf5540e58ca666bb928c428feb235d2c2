const createTradesTableQuery = `
    CREATE TABLE IF NOT EXISTS alura.trades (
      id SERIAL PRIMARY KEY,
      frequency VARCHAR(100) NOT NULL,
      closed BOOLEAN NOT NULL DEFAULT FALSE,
      pair VARCHAR(100) NOT NULL,
      is_long BOOLEAN NOT NULL,
      collateral NUMERIC(20, 10) NOT NULL,
      leverage NUMERIC(10, 2) NOT NULL,
      creator_address VARCHAR(100),
      agent_address VARCHAR(100),
      created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
  `;

export default createTradesTableQuery;

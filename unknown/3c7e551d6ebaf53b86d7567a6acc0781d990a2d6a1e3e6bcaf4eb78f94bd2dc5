import { Request, Response, NextFunction } from 'express';
import jwt, { JwtPayload, VerifyErrors } from 'jsonwebtoken';
import { IUser } from '@/db/schemas/user';

export const authenticateToken = (req: Request, res: Response, next: NextFunction): void => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    res.status(401).json({
      error: 'Unauthorized',
      message: 'Access token is missing or invalid',
    });
    return;
  }

  jwt.verify(
    token,
    process.env.JWT_SECRET as string,
    (err: VerifyErrors | null, decoded: JwtPayload | string | undefined) => {
      if (err) {
        if (err instanceof jwt.TokenExpiredError) {
          res.status(401).json({
            error: 'Unauthorized',
            message: 'Access token has expired',
          });
        } else if (err instanceof jwt.JsonWebTokenError) {
          res.status(403).json({
            error: 'Forbidden',
            message: 'Invalid access token',
          });
        } else {
          res.status(500).json({
            error: 'Internal Server Error',
            message: 'An unexpected error occurred during token validation',
          });
        }
        return;
      }

      // Validate decoded payload structure
      if (decoded && typeof decoded === 'object' && 'id' in decoded && 'address' in decoded) {
        req.user = decoded as IUser; // Type assertion here
        next();
      } else {
        res.status(400).json({
          error: 'Bad Request',
          message: 'Invalid user payload in token',
        });
        return;
      }
    },
  );
};

export default authenticateToken;

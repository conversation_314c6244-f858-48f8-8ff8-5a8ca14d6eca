import { createCipheriv, randomBytes } from 'crypto';

// Use environment variables for secrets (never hardcode!)
const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY!; // Must be 32 bytes for AES-256
const IV_LENGTH = 16; // AES IV length

// Encrypt the private key
export default function encryptPrivateKey(privateKey: string): string {
  if (!privateKey) {
    throw new Error('Private key is undefined or null');
  }

  const iv = randomBytes(IV_LENGTH);
  const cipher = createCipheriv('aes-256-cbc', Buffer.from(ENCRYPTION_KEY, 'hex'), iv);
  let encrypted = cipher.update(privateKey, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return `${iv.toString('hex')}:${encrypted}`;
}

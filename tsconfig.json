{
  "compilerOptions": {
    "target": "ESNext",
    "module": "NodeNext",
    "moduleResolution": "nodenext",
    "strict": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "outDir": "./dist",
    "rootDir": "./src",
    "baseUrl": "./",
    "paths": {
      "@/*": ["src/*"]
    },
    "resolveJsonModule": true,
    "typeRoots": ["node_modules/@types", "src/types"] // Include custom types
  },
  "exclude": ["node_modules"],
  "include": ["src/types/global.d.ts", "src/**/*.ts", "src/db/indexer/token-list.json"], // Ensure all custom types are included
  "files": ["src/types/global.d.ts"]
}

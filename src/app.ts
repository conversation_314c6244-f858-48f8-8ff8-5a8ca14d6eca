// src/app.ts
import express from 'express';
import cors from 'cors';
import apiRoutes from './routes/index';
import dotenv from 'dotenv';
import { initializeCronJobs } from '@/utils/cron-job';

dotenv.config();

const app = express();

// Middleware to handle CORS
app.use(cors());

// Initialize cron jobs
initializeCronJobs();

// Middleware to parse incoming requests with JSON payloads
app.use(express.json());

app.use(express.urlencoded({ extended: true }));

// Routes
app.use('/api/', apiRoutes);

export default app;

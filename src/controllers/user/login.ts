import { Request, Response, NextFunction } from 'express';
import nacl from 'tweetnacl';
import jwt from 'jsonwebtoken';
import { stripHexPrefix } from 'ethjs-util';
import { decodeBase64 } from 'tweetnacl-util';
import * as agentService from '@/services/agent';
import * as userService from '@/services/user';
import createAptosWallet from '@/utils/blockchain/wallet/createWallet';
import { createHash } from 'crypto';

// Utility function to convert a BigInt to a buffer
function bigIntToBuffer(value: BigInt): Uint8Array {
  let hex = value.toString(16);
  if (hex.length % 2 !== 0) hex = '0' + hex; // Ensure even-length hex string
  return Uint8Array.from(Buffer.from(hex, 'hex'));
}

// Helper function to validate and convert expiry time
function validateExpiryTime(expiry: string | undefined): any {
  if (!expiry) return undefined;

  // If it's a pure number string, convert to number
  if (/^\d+$/.test(expiry)) {
    return Number(expiry);
  }

  // Check if it matches the StringValue format
  const match = expiry.toLowerCase().match(/^(\d+)\s*(s|m|h|d|w|y)$/);

  if (match) {
    return expiry;
  }

  throw new Error(
    'Invalid expiry time format. Must be a number or a string like "24h", "7d", etc.',
  );
}

const login = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const { message, wallet_address, signature, publicKey } = req.body;

    const pubkey = BigInt('0x' + stripHexPrefix(publicKey));
    const signatureBytes = BigInt('0x' + stripHexPrefix(signature));

    // Decode message from Base64 to bytes
    const messageBytes = decodeBase64(message);

    // Verify signature
    const verified = nacl.sign.detached.verify(
      messageBytes,
      bigIntToBuffer(signatureBytes),
      bigIntToBuffer(pubkey),
    );

    if (!verified) {
      res.status(500).json({ msg: 'Verification failed.' });
      return;
    }
    
    // Verify wallet address matches the public key
    const pubkeyBytes = bigIntToBuffer(pubkey);
    if (pubkeyBytes.length !== 32) {
      res.status(400).json({ msg: 'Invalid public key length, expected 32 bytes' });
      return;
    }
    if (!/^0x[0-9a-fA-F]{64}$/.test(wallet_address)) {
      res.status(400).json({ msg: 'Invalid wallet address format' });
      return;
    }
    const authKeyBytes = new Uint8Array(33);
    authKeyBytes.set(pubkeyBytes, 0);
    authKeyBytes[32] = 0;
    const derivedAddressBytes = createHash('sha3-256').update(Buffer.from(authKeyBytes)).digest();
    const derivedAddress = '0x' + derivedAddressBytes.toString('hex');
    if (derivedAddress.toLowerCase() !== wallet_address.toLowerCase()) {
      res.status(400).json({ msg: 'Wallet address does not match the public key' });
      return;
    }

    // Check if user exists or create a new user
    let user = await userService.getUserByAddress(wallet_address);
    let agent;
    if (!user) {
      user = await userService.createUser({ address: wallet_address });
      const wallet = createAptosWallet();
      agent = await agentService.createAgent({
        userId: user.id,
        publicKey: wallet.address,
        privateKey: wallet.encryptedPrivateKey,
      });
    }
    agent = await agentService.getAgentByUserId(user.id);

    // Validate environment variables
    if (!process.env.JWT_SECRET) throw new Error('JWT_SECRET is not defined');
    if (!process.env.JWT_EXPIRY) throw new Error('JWT_EXPIRY is not defined');

    // Validate and convert expiry time
    const expiresIn = validateExpiryTime(process.env.JWT_EXPIRY);
    if (!expiresIn) throw new Error('Invalid JWT_EXPIRY format');

    // Create the payload
    const payload = {
      address: user.address,
      id: user.id,
    };

    // Create sign options with proper typing
    const signOptions: jwt.SignOptions = {
      expiresIn,
    };

    // Generate token with proper typing
    const token = jwt.sign(payload, process.env.JWT_SECRET, signOptions);

    res.json({
      token: `Bearer ${token}`,
      user,
      agent: agent?.publicKey,
    });
  } catch (error) {
    next(error); // Pass errors to Express error handler
  }
};

export default login;
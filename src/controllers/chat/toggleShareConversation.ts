import { Request, Response } from 'express';
import * as conversationService from '@/services/chat';

const toggleShareConversation = async (req: Request, res: Response): Promise<void> => {

  // Ensure the user is authenticated
  if (!req.user?.id) {
    res.status(401).json({ error: 'Unauthorized' });
    return;
  }

  const conversationId = parseInt(req.params.conversationId as string, 10);
  if (isNaN(conversationId)) {
    res.status(400).json({ error: 'Invalid conversation ID' });
    return;
  }

  const conversation = await conversationService.getConversationObjByConversationId(conversationId);

  if (!conversation) {
    res.status(404).json({ error: 'Conversation not found' });
    return;
  }
  // Check if the user is the owner of the conversation
  if (conversation.userId !== req.user.id) {
    res.status(403).json({ error: 'Forbidden: You are not the owner of this conversation' });
    return;
  }
  
  const nextStatus = !conversation.isPublic;
  await conversationService.updateConversationStatus(conversation.id, nextStatus);
  res.status(200).json({message: `Conversation sharing is now ${nextStatus ? 'enabled' : 'disabled'}.`});
  return;
};

export default toggleShareConversation;

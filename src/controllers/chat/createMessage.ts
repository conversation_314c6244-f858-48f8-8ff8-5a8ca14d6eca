import { Request, Response } from 'express';
import * as chatService from '@/services/chat';

const createMessage = async (req: Request, res: Response): Promise<void> => {
  try {
    const conversationId = Number(req.params.conversationId);
    const userId = req.user?.id;
    const { data, messageType, message } = req.body;

    if (!messageType || !message) {
      res.status(400).json({ message: 'Message type and message is required' });
      return;
    }

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    // Fetch the conversation by ID and user ID
    const conversation = await chatService.getConversationById(userId, conversationId);

    if (!conversation) {
      res.status(404).json({ message: 'Conversation not found' });
      return;
    }
    
    const messageData = {
      senderId: conversation.agent.id,
      receiverId: userId,
      message: message,
      messageType: messageType,
      data: data || {},
      isAgent: true,
    };

    const response = await chatService.createMessage({
      ...messageData,
      conversationId: conversation.id,
    });

    res.status(200).json(response);
    return;
  } catch (error) {
    console.error('Error in createMessage:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export default createMessage;

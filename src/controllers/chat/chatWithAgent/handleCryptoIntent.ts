import getCoinData, { getPriceDetails } from '@/utils/blockchain/indexer/getCoinData';


const handleCryptoIntent = async (cyrptoData: any) => {
    const finalData: any = {};
    let itemBuy;
    let itemSell;
    if (['close_position', 'long', 'short'].includes(cyrptoData.intent)) {
      const pairId = `${cyrptoData.item.toUpperCase()}_USD`;
      finalData.leverage = cyrptoData.leverage;
      finalData.collateral = cyrptoData.amount;
      finalData.event = cyrptoData.intent;
      finalData.pair = pairId;
      return finalData;
    }

    itemBuy = await getCoinData(cyrptoData.item_buy || 'apt');
    itemSell = await getCoinData(cyrptoData.item_sell || 'apt');

    if (itemSell) itemSell.amount = cyrptoData.amount;

    if (process.env.NETWORK === 'testnet') {
      const quote = await getPriceDetails(
        itemSell.wrapAddress,
        itemBuy.wrapAddress,
        cyrptoData.amount ?? 1,
      );
      finalData.quote = quote;
    }
    finalData.intent = cyrptoData.dca ? 'dca' : cyrptoData.snipe ? 'snipe' : 'swap';
    finalData.itemBuy = itemBuy;
    finalData.itemSell = itemSell;
    return finalData;
}

export default handleCryptoIntent;
import { Request, Response } from 'express';
import * as chatService from '@/services/chat';
import * as userService from '@/services/user';
import * as tradeService from '@/services/trade';
import LlmClient from '@/utils/ai/llmModels';
import { AIAgentMessage } from '@/types/common';
import { IChat } from '@/db/schemas';
import handleCryptoIntent from './handleCryptoIntent';
import { parsePrompt } from '@/utils/ai/llmModels/prompts/classifyInvestPrompt';
import { TRADE_STATUS } from '@/db/schemas/constants/trade';
import generateStrategy from '@/utils/trade/generateStrategy';

const prepareMessages = (messages: IChat[]): AIAgentMessage[] => {
  return messages
    .map(message => {
      return {
        role: message.isAgent ? ('assistant' as const) : ('user' as const),
        content: message.message,
      };
    })
    .reverse();
};

const chatWithAgent = async (req: Request, res: Response): Promise<void> => {
  try {

    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const user = (await userService.getUserById(userId)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ message: 'Agent not found' });
      return;
    }

    if (user.remainingPrompts < 1) {
      res.status(400).json({ error: 'Insufficient prompts' });
      return;
    }

    const { message } = req.body;

    let conversationId = req.body.conversationId ? Number(req.body.conversationId) : null;

    if (!conversationId) {
      let title = (await LlmClient.getConversationTitleFromMessage(message)) || undefined;
      if (!title) {
        title = message.substring(0, 20);
      }
      const conversation = await chatService.createConversation(
        user.agent.id,
        userId,
        title,
      );
      if (!conversation) {
        res.status(500).json({ message: 'Failed to create conversation' });
        return;
      }
      conversationId = conversation.id;
    }

    // Fetch the conversation by ID
    const conversation = await chatService.getConversationByIdWithChats(conversationId);

    if (!conversation) {
      res.status(404).json({ message: 'Conversation not found' });
      return;
    }

    const messageType = await LlmClient.classifyText(message);

    if (messageType === 'crypto_intent') {
      const cyrptoData = await LlmClient.classifyTextIntent(message);
      if (cyrptoData.type === 'coin' || cyrptoData.type === 'token') {
        const finalData = await handleCryptoIntent(cyrptoData);
        res.status(200).json(finalData);
        return;
      }
    } else if(messageType === 'profit_invest') {
      const tradeExists = await tradeService.checkIfOpenTradeExists(userId);
      if(tradeExists) {
        res.status(400).json({ error: 'Trade Already Exists - You Are Only Allowed To Run One Perp Prompt (Ex, I Want To Make $10 By Investing $100" At A Time. Please Wait For The Positions To Hit Profit Goal or Closed or Liquidated To Run Another Prompt. In the meantime You Can Swap, DCA Or Snipe.' });
        return;
      }

      const { budget, profitTarget } = parsePrompt(message);

      if (typeof budget !== 'number' || typeof profitTarget !== 'number') {
        res.status(400).json({ error: 'Invalid budget or profitTarget' });
        return;
      }

      if (budget < 50 || budget > 5000) {
        res.status(400).json({ error: 'Budget must be between $50 and $5000' });
        return;
      }
      if (profitTarget < 5 || profitTarget > 10000) {
        res.status(400).json({ error: 'Profit goal must be between $5 and $10000' });
        return;
      }

      await chatService.createChat({
        conversationId: conversation.id,
        senderId: userId,
        receiverId: conversation.agentId,
        message: message,
        messageType: messageType,
        isAgent: false,
      });
      if (typeof budget !== 'number' || typeof profitTarget !== 'number') {
        res.status(400).json({ error: 'Invalid budget or profitTarget' });
        return;
      }

      const roi = (profitTarget / budget) * 100;
      const initialResponse = `With the user's intent to grow an investment of ${budget} into ${budget + profitTarget}, our intelligent trading strategy is designed to meet a **${roi}%** Return on Investment (ROI) goal.`
      
      const chatResponse = await chatService.createChat({
        conversationId: conversation.id,
        senderId: conversation.agent.id,
        receiverId: userId,
        message: initialResponse,
        messageType: messageType,
        isAgent: true,
      });

      const trade = await tradeService.createTrade({
        userId,
        agentId: user.agent.id,
        prompt: message,
        budget,
        profitGoal: profitTarget,
        chatId: chatResponse.id,
        status: TRADE_STATUS.PENDING,
      });

      await userService.updateRemainingPrompts(user.id, user.remainingPrompts - 1);

      generateStrategy({
        tradeId: trade.id,
        budget,
        profitGoal: profitTarget,
        conversationId: conversation.id,
        userId,
        agentId: user.agent.id,
      });

      res.status(200).json({...chatResponse, tradeId: trade.id});
      return;
    }

    // Save the user's message to the database
    const newData = await chatService.createChat({
      conversationId: conversation.id,
      senderId: userId,
      receiverId: conversation.agentId,
      message: message,
      messageType: messageType,
      isAgent: false,
    });

    const finalChats = conversation.chats.concat(newData as IChat);

    // Prepare messages for the LLM
    const messages = prepareMessages(finalChats);

    // Get the AI agent's response
    const response = await LlmClient.chatWithAgent({
      userMessage: message,
      agentName: 'LLM Agent',
      agentDescription: 'Answers general questions. Inclined towards crypto',
      messages,
      knowledgeBase: '',
      maxTokens: 1000,
    });

    // Save the AI agent's response to the database
    const chatResponse = await chatService.createChat({
      conversationId: conversation.id,
      senderId: conversation.agent.id,
      receiverId: userId,
      message: response || '',
      messageType: messageType,
      isAgent: true,
    });

    await userService.updateRemainingPrompts(user.id, user.remainingPrompts - 1);

    res.status(200).json(chatResponse);
    return;
  } catch (error) {
    console.error('Error in chatWithAgent:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export default chatWithAgent;

import { Request, Response } from 'express';
import * as chatService from '@/services/chat';

const list = async (req: Request, res: Response): Promise<void> => {
  try {
    const conversationId = Number(req.params.conversationId);
    const page = Number(req.query.page) || 1;
    const limit = Number(req.query.limit) || 10;
    const userId = req.user?.id;

    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    // Fetch the conversation by ID and user ID
    const conversation = await chatService.getConversationObjByConversationId(conversationId);
    if(!conversation?.isPublic && conversation?.userId !== userId) {
      res.status(403).json({ message: 'Forbidden: You do not have access to this conversation' });
      return;
    }

    if (!conversation) {
      res.status(404).json({ message: 'Conversation not found' });
      return;
    }

    // Fetch paginated chats for the conversation
    const data = await chatService.getChatsByConversationIdWithPagination(
      conversationId,
      page,
      limit,
    );

    res.status(200).json(data);
    return;
  } catch (error) {
    console.error('Error in list:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export default list;

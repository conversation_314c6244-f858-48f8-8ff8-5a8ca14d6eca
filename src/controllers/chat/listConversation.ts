import { Request, Response } from 'express';
import * as conversationService from '@/services/chat';
import * as userService from '@/services/user';

const listConversations = async (req: Request, res: Response): Promise<void> => {

  // Ensure the user is authenticated
  if (!req.user?.id) {
    res.status(401).json({ error: 'Unauthorized' });
    return;
  }

  // Retrieve the user, and ensure they have an associated agent
  const user = (await userService.getUserById(req.user.id)) as any;
  if (!user.agent) {
    res.status(401).json({ error: 'User does not have an agent' });
    return;
  }
  const agent = user.agent;

  // Get pagination parameters from query string, with default values
  const page = parseInt(req.query.page as string, 10) || 1;
  const limit = parseInt(req.query.limit as string, 10) || 10;

  try {
    // Fetch conversations using pagination for the given agent and conversation type
    const conversations = await conversationService.getConversationWithPagination(
      user.id,
      agent.id,
      page,
      limit,
    );
    res.status(200).json(conversations);
  } catch (error) {
    console.error('Error fetching conversations:', error);
    res.status(500).json({ error: 'Failed to fetch conversations' });
  }
};

export default listConversations;

import { Request, Response } from 'express';
import * as userService from '@/services/user';
import * as agentService from '@/services/agent';
import decryptPrivateKey from '@/utils/blockchain/cryptography/decryptPrivateKey';
import { transferTokens } from '@/utils/blockchain/transaction/coinTxnBuilder';

const buyCredit = async (req: Request, res: Response): Promise<void> => {
  try {
    const receiverAddress = process.env.RECEIVER_ADDRESS || '';
    if (!receiverAddress) {
      res.status(400).json({ error: 'Setting is not properly configured' });
      return;
    }

    const userId = req.user?.id;
    if (!userId) {
      res.status(401).json({ message: 'Unauthorized' });
      return;
    }

    const user = (await userService.getUserById(userId)) as any;

    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }

    const agent = await agentService.getAgentDetail(user.agent.id);
    const privateKey = agent.privateKey;

    if (!privateKey) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    
    const encryptedPrivateKey = decryptPrivateKey(privateKey);

    const perPromptCost = Number(process.env.PROMPT_PER_COST || 0.05);
    const requestedPrompt = Number(req.body.totalPrompt) || 20;
    
    // Add validation for reasonable limits
    if (requestedPrompt <= 0 || requestedPrompt > 10000) {
      res.status(400).json({ error: 'Invalid prompt amount. Must be between 1 and 10000.' });
      return;
    }
    
    const amountPaid = requestedPrompt * perPromptCost;

    // Add balance check before attempting transaction
    console.log(`Attempting to transfer ${amountPaid} APT for ${requestedPrompt} prompts`);

    const response = await transferTokens(
      amountPaid.toString(),
      '0x1::aptos_coin::AptosCoin',
      receiverAddress,
      encryptedPrivateKey,
    );

    if (!response.success) {
      res.status(400).json({ 
        error: 'Transaction failed',
        details: 'Please check your wallet balance and try again'
      });
      return;
    }

    const purchaseData = {
      userId,
      transactionHash: response.hash,
      amountPaid: amountPaid,
      boughtCredit: requestedPrompt,
    };

    const totalPrompts = user.remainingPrompts + requestedPrompt;
    
    // Use transaction for database operations to ensure consistency
    await Promise.all([
      userService.addNewPurchase(purchaseData),
      userService.updateRemainingPrompts(userId, totalPrompts)
    ]);

    res.status(200).json({ 
      message: 'Credit topup successful', 
      data: { 
        transactionHash: response.hash,
        promptsAdded: requestedPrompt,
        totalPrompts: totalPrompts,
        amountPaid: amountPaid
      } 
    });

    return;
  } catch (error) {
    console.error('Error in buyCredit:', error);
    
    // More specific error handling
    let errorMessage = 'Internal server error';
    let statusCode = 500;
    
    if (error instanceof Error) {
      if (error.message.includes('insufficient funds') || error.message.includes('INSUFFICIENT_BALANCE')) {
        errorMessage = 'Insufficient balance in wallet';
        statusCode = 400;
      } else if (error.message.includes('gas')) {
        errorMessage = 'Transaction gas configuration error';
        statusCode = 400;
      } else if (error.message.includes('network') || error.message.includes('connection')) {
        errorMessage = 'Network connection error. Please try again.';
        statusCode = 503;
      }
    }
    
    res.status(statusCode).json({
      message: errorMessage,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export default buyCredit;
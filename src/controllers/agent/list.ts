import { Request, Response } from 'express';
import * as agentService from '@/services/agent';


const listAgent = async (req: Request, res: Response): Promise<void> => {
  try {
    // Extract pagination parameters from query
    const page = parseInt(req.query.page as string) || 1; // Default to page 1
    const pageSize = parseInt(req.query.pageSize as string) || 10; // Default to 10 items per page
    
    // Call service with pagination params
    const { agents, total } = await agentService.getAllAgents(page, pageSize);
    
    // Calculate pagination metadata
    const totalPages = Math.ceil(total / pageSize);
    
    // Respond with success and pagination metadata
    res.status(200).json({
      agents,
      totalItems: total,
      totalPages,
      currentPage: page,
      itemsPerPage: pageSize,
    });
    return;
  } catch (error) {
    console.error('Error in listAgent:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export default listAgent;

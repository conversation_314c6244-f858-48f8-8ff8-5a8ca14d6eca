import { Request, Response } from 'express';
import * as userService from '@/services/user';
import * as agentService from '@/services/agent';
import decryptPrivateKey from '@/utils/blockchain/cryptography/decryptPrivateKey';
import { buyNFTs, listNFTs } from '@/utils/blockchain/transaction/nftTxnBuilder';
import { insertAgentActivity } from '@/db/indexer/trade';

const performNFTTransaction = async (req: Request, res: Response): Promise<void> => {
  try {
    const collectionInfo = JSON.stringify(req.body?.collectionInfo ?? {});

    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }

    const { listingId, nftId, price, functionArguments } = req.body;

    const user = (await userService.getUserById(req.user?.id)) as any;

    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }

    const agent = await agentService.getAgentDetail(user.agent.id);
    const privateKey = agent.privateKey;

    if (!privateKey) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }

    const encryptedPrivateKey = decryptPrivateKey(privateKey);
    if (nftId && price) {
      const result = await listNFTs(encryptedPrivateKey, functionArguments);
      const agent_activity = {
        type: 'nft',
        event_type: 'list',
        creator_address: user.address,
        agent_address: user.agent.publicKey,
        transaction_timestamp: new Date().toISOString(),
        activity: collectionInfo,
      };
      await insertAgentActivity(agent_activity);
      res
        .status(200)
        .json({ message: 'NFT listed successfully', data: { transactionHash: result } });
      return;
    }

    if (listingId) {
      const result = await buyNFTs(encryptedPrivateKey, functionArguments);
      const agent_activity = {
        type: 'nft',
        event_type: 'buy',
        creator_address: user.address,
        agent_address: user.agent.publicKey,
        transaction_timestamp: new Date().toISOString(),
        activity: collectionInfo,
      };
      await insertAgentActivity(agent_activity);
      res
        .status(200)
        .json({ message: 'NFT bought successfully', data: { transactionHash: result } });
      return;
    }

    res.status(400).json({ message: 'Could not perform NFT transaction' });
    return;
  } catch (error) {
    console.error('Error in trainAgent:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

export default performNFTTransaction;

import { Request, Response } from 'express';
import * as userService from '@/services/user';
import * as agentService from '@/services/agent';
import decryptPrivateKey from '@/utils/blockchain/cryptography/decryptPrivateKey';
import {
  closePosition,
  getPositions,
  openLimitOrder,
  openMarketOrder,
} from '@/utils/blockchain/transaction/tradeTransactions';
import { insertTrade, setTradeClosed } from '@/db/indexer/trade';
import { fetchOpenDCARecords } from '@/db/indexer/dca';
import { getAandInsertTradeHistory, getNetPnlForAddress } from '@/db/indexer/positionHistory';

function getFrequencyString(frequency: '5m' | '1H' | '3H' | '6H' | '12H' | '1D' | '7D'): string {
  const availableTimestamps = {
    '5m': '5 minutes',
    '1H': '1 hour',
    '3H': '3 hours',
    '6H': '6 hours',
    '12H': '12 hours',
    '1D': '1 day',
    '7D': '7 days',
  };

  return availableTimestamps[frequency];
}
export const openMarketPosition = async (req: Request, res: Response): Promise<void> => {
  try {
    const { pair, isLong, collateral, leverage, tp, sl, frequency } = req.body;

    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    const privateKey = agent.privateKey;
    if (!privateKey) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const encryptedPrivateKey = decryptPrivateKey(privateKey);
    const { success, hash, sender, timestamp } = await openMarketOrder(
      encryptedPrivateKey,
      pair,
      leverage,
      collateral,
      isLong,
    );
    const frequencyString = getFrequencyString(frequency);

    if (success && frequencyString) {
      await insertTrade({
        frequency,
        pair,
        isLong,
        collateral,
        leverage,
        creatorAddress: req.user?.address,
        agentAddress: sender.toString(),
      });
    }

    res.status(200).json({ message: 'Position opened.', data: { transactionRes: hash } });
    return;
  } catch (error) {
    console.error('Error in buyAgentTokens:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};
export const openLimitPosition = async (req: Request, res: Response): Promise<void> => {
  try {
    const { pair, isLong, price, collateral, leverage, tp, sl } = req.body;

    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    const privateKey = agent.privateKey;
    if (!privateKey) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const encryptedPrivateKey = decryptPrivateKey(privateKey);
    const { success, hash, sender, timestamp } = await openLimitOrder(
      encryptedPrivateKey,
      pair,
      leverage,
      collateral,
      price,
      isLong,
    );

    res
      .status(200)
      .json({ message: 'Position opened.', data: { success, hash, sender, timestamp } });
    return;
  } catch (error) {
    console.error('Error in buyAgentTokens:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};
export const closeTradePosition = async (req: Request, res: Response): Promise<void> => {
  try {
    const { pair, isLong } = req.body;
    if (!pair) {
      res.status(400).json({ error: 'Invalid request' });
      return;
    }
    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    const privateKey = agent.privateKey;
    if (!privateKey) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const encryptedPrivateKey = decryptPrivateKey(privateKey);
    const { success, hash, sender, timestamp } = await closePosition(
      encryptedPrivateKey,
      pair,
      isLong,
    );
    if (success) {
      await setTradeClosed(sender, pair, isLong);
    }

    res
      .status(200)
      .json({ message: 'Position closed.', data: { success, hash, sender, timestamp } });
    return;
  } catch (error) {
    console.error('Error in buyAgentTokens:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};
export const getTradePositions = async (req: Request, res: Response): Promise<void> => {
  try {
    const { pair } = req.body;

    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    const privateKey = agent.privateKey;
    if (!privateKey) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const encryptedPrivateKey = decryptPrivateKey(privateKey);
    const result = await getPositions(encryptedPrivateKey);

    res.status(200).json({ message: 'Positions fetched.', data: { positions: result } });
    return;
  } catch (error) {
    console.error('Error in getTradePositions:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};
export const getOpenDCAOrders = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token_address } = req.body;

    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    const agent_address = agent.publicKey?.toString();
    if (!agent_address) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const result = await fetchOpenDCARecords(agent_address, token_address);

    res.status(200).json({ message: 'Orders fetched.', data: { orders: result } });
    return;
  } catch (error) {
    console.error('Error in getting dca orders:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};
export const syncTradeHistory = async (req: Request, res: Response): Promise<void> => {
  try {
    const { token_address } = req.body;

    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    const agent_address = agent.publicKey?.toString();
    if (!agent_address) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const result = await getAandInsertTradeHistory(agent_address);
    res.status(200).json({ message: 'Trades synced.', data: result });
    return;
  } catch (error) {
    console.error('Error in syncTradeHistory:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};
export const getRealizedPnl = async (req: Request, res: Response): Promise<void> => {
  try {
    if (!req.user?.id) {
      res.status(401).json({ error: 'Unauthorized' });
      return;
    }
    const user = (await userService.getUserById(req.user?.id)) as any;
    if (!user?.agent?.id) {
      res.status(400).json({ error: 'Agent not found' });
      return;
    }
    const agent = await agentService.getAgentDetail(user.agent.id);
    const agent_address = agent.publicKey?.toString();
    if (!agent_address) {
      res.status(400).json({ error: 'Agent wallet not found' });
      return;
    }
    const result = await getNetPnlForAddress(agent_address);
    res.status(200).json({
      message: 'Net pnl fetched.',
      data: {
        realizedPnl: result,
      },
    });
    return;
  } catch (error) {
    console.error('Error in getRealizedPnl:', error);
    res.status(500).json({
      message: 'Internal server error',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    });
    return;
  }
};

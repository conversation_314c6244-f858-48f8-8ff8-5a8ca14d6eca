import {
  pgTable,
  serial,
  varchar,
  integer,
  numeric,
  index,
  boolean,
} from 'drizzle-orm/pg-core';
import { timestamps } from '../helpers/column';
import { relations } from 'drizzle-orm';
import { users } from './user';
import { agents } from './agent';
import { chats } from './chat';

export const trades = pgTable(
  'trades',
  {
    id: serial('id').primaryKey(),
    userId: integer('user_id').notNull().references(() => users.id),
    agentId: integer('agent_id').notNull().references(() => agents.id),
    chatId: integer('chat_id').references(() => chats.id),
    prompt: varchar('prompt', { length: 1024 }).notNull(),
    budget: integer('budget').notNull(),
    profitGoal: integer('profit_goal').notNull(),
    status: varchar('status', { length: 32 }).notNull(),
    totalPnl: numeric('total_pnl', { precision: 15, scale: 8 }),
    ...timestamps,
  },
  table => [
    index('trades_user_id_idx').on(table.userId),
    index('trades_agent_id_idx').on(table.agentId),
    index('trades_chat_id_idx').on(table.chatId),
  ],
);

export const positions = pgTable(
  'positions',
  {
    id: serial('id').primaryKey(),
    tradeId: integer('trade_id').notNull().references(() => trades.id),
    pair: varchar('pair', { length: 32 }).notNull(),
    budget: numeric('budget', { precision: 15, scale: 4 }).notNull(),
    marketRegime: varchar('market_regime', { length: 255 }),
    entryPrice: numeric('entry_price', { precision: 15, scale: 8 }),
    exitPrice: numeric('exit_price', { precision: 15, scale: 8 }),
    pnl: numeric('pnl', { precision: 15, scale: 8 }),
    direction: varchar('direction', { length: 32 }),
    leverage: numeric('leverage', { precision: 8, scale: 2 }),
    signalScore: numeric('signal_score', { precision: 8, scale: 4 }),
    rsi: numeric('rsi', { precision: 8, scale: 4 }),
    macdHist: numeric('macd_hist', { precision: 8, scale: 4 }),
    emaSlope: numeric('ema_slope', { precision: 8, scale: 4 }),
    atrPct: numeric('atr_pct', { precision: 8, scale: 4 }),
    txHash: varchar('tx_hash', { length: 255 }),
    status: varchar('status', { length: 32 }).notNull(),
    reason: varchar('reason', { length: 255 }),
    ...timestamps,
  },
  table => [
    index('trade_id_idx').on(table.tradeId),
    index('positions_pair_idx').on(table.pair),
    index('positions_status_idx').on(table.status),
  ],
);

export const logs = pgTable(
  'logs',
  {
    id: serial('id').primaryKey(),
    tradeId: integer('trade_id').notNull().references(() => trades.id),
    text: varchar('text', { length: 2048 }).notNull(),
    logType: varchar('log_type', { length: 64 }).notNull(),
    ...timestamps,
  },
  table => [
    index('logs_trade_id_idx').on(table.tradeId),
    index('logs_log_type_idx').on(table.logType),
  ],
);

export const tradesRelations = relations(trades, ({ one, many }) => ({
  user: one(users, { fields: [trades.userId], references: [users.id] }),
  agent: one(agents, { fields: [trades.agentId], references: [agents.id] }),
  chat: one(chats, { fields: [trades.chatId], references: [chats.id] }),
  positions: many(positions),
  logs: many(logs),
}));

export const positionsRelations = relations(positions, ({ one }) => ({
  trade: one(trades, { fields: [positions.tradeId], references: [trades.id] }),
}));

export const logsRelations = relations(logs, ({ one }) => ({
  trade: one(trades, { fields: [logs.tradeId], references: [trades.id] }),
}));

export type ITrade = typeof trades.$inferSelect;
export type IPosition = typeof positions.$inferSelect;
export type ILog = typeof logs.$inferSelect;

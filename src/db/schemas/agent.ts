import {
  pgTable,
  serial,
  varchar,
  integer,
} from 'drizzle-orm/pg-core';
import { timestamps } from '@/db/helpers/column';
import { relations } from 'drizzle-orm';
import { users } from '@/db/schemas';

export const agents = pgTable('agents', {
  id: serial('id').primaryKey(),

  publicKey: varchar('public_key', { length: 255 }),
  privateKey: varchar('private_key', { length: 255 }),

  // User relationship
  userId: integer('user_id')
    .references(() => users.id, {
      onDelete: 'cascade',
    })
    .notNull(),

  ...timestamps,
});

export const agentRelations = relations(agents, ({ one }) => ({
  user: one(users, { fields: [agents.userId], references: [users.id] }),
}));

// Infer the `Agent` type
export type IAgent = typeof agents.$inferSelect;

export type ISafeAgent = Omit<
  IAgent,
  | 'contractAddress'
  | 'agentContract'
  | 'reserveAddress'
  | 'creatorAddress'
  | 'personalDetail'
  | 'privateKey'
>;

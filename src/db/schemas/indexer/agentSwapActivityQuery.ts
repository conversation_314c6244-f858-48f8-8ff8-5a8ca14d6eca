const swapsByAgentsQuery = `
CREATE TABLE IF NOT EXISTS alura.agent_swap_activity (
  id SERIAL PRIMARY KEY,
  transaction_hash VARCHAR(100) UNIQUE,
  sender VARCHAR(100),
  transaction_timestamp TIMESTAMP DEFAULT NOW(),
  from_token VARCHAR(250),
  to_token VARCHAR(250),
  from_amount NUMERIC(30, 10),
  to_amount NUMERIC(30, 10),
  success BOOLEAN,
  order_id INTEGER,
  source VARCHAR(16) NOT NULL CHECK (source IN ('NORMAL', 'DCA', 'SNIPE')),
  created_at TIMESTAMP DEFAULT NOW()
);
`;

export default swapsByAgentsQuery;

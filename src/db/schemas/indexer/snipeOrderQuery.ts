const createSnipeOrdersQuery = `
CREATE TABLE IF NOT EXISTS alura.snipe_orders (
  id serial PRIMARY KEY,
  target_token_address VARCHAR(100) NOT NULL,
  base_token_address VARCHAR(255) NOT NULL,
  reference_price NUMERIC(30, 15) NOT NULL,
  price_change_threshold NUMERIC NOT NULL, -- e.g., 5.0 for 5%
  direction VARCHAR(100) NOT NULL, -- 'up' or 'down'
  buy_amount NUMERIC NOT NULL,
  target_token_amount NUMERIC NOT NULL DEFAULT 0,
  polling_interval INTEGER NOT NULL, -- in seconds
  slippage_tolerance NUMERIC NOT NULL, -- e.g., 0.01 for 1%
  user_wallet_address VARCHAR(100) NOT NULL,
  agent_wallet_address VARCHAR(100) NOT NULL,
  take_profit_percent NUMERIC, -- optional: trigger take-profit (e.g., 10%)
  stop_loss_percent NUMERIC,   -- optional: trigger stop-loss (e.g., -5%)
  closed BOOLEAN DEFAULT FALSE,
  executed BOOLEAN DEFAULT FALSE,
  tp_price NUMERIC(30, 15),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
)`;

export default createSnipeOrdersQuery;

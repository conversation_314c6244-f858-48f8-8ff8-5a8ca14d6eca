import createTradesTableQuery from './tradeOrderQuery';
import createDCATableQuery from './dcaOrderQuery';
import createSnipeOrdersQuery from './snipeOrderQuery';
import swapsByAgentsQuery from './agentSwapActivityQuery';
import positionHistoryQuery from './positionHistoryQuery';
import { Pool } from 'pg';

export async function initializeTables(indexerDbPool: Pool) {
  const queries = [
    { name: 'trades', sql: createTradesTableQuery },
    { name: 'dcas', sql: createDCATableQuery },
    { name: 'snipe_orders', sql: createSnipeOrdersQuery },
    { name: 'agent_swap_activity', sql: swapsByAgentsQuery },
    { name: 'position_history', sql: positionHistoryQuery },
  ];

  const client = await indexerDbPool.connect();

  try {
    await client.query('BEGIN');

    for (const { name, sql } of queries) {
      await client.query(sql);
    }

    await client.query('COMMIT');
    console.log('🎉 Tables initialized');
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

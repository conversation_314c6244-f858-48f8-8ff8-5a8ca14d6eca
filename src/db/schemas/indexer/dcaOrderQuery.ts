const createDCATableQuery = `
    CREATE TABLE IF NOT EXISTS alura.dcas (
      id SERIAL PRIMARY KEY,
      frequency VARCHAR(100) NOT NULL,
      closed BOOLEAN NOT NULL DEFAULT FALSE,
      from_token VARCHAR(255) NOT NULL,
      to_token VARCHAR(255) NOT NULL,
      amount NUMERIC(20, 5) NOT NULL,
      quote NUMERIC(20, 5),
      creator_address VARCHAR(100),
      agent_address VARCHAR(100),
      created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
    );
  `;

export default createDCATableQuery;

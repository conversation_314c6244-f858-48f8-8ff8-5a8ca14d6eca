const positionHistoryQuery = `
CREATE TABLE IF NOT EXISTS alura.position_history (
  id SERIAL PRIMARY KEY,
  version NUMERIC(20, 0) UNIQUE NOT NULL,
  type VARCHAR(255) NOT NULL,
  sequence_number BIGINT NOT NULL,
  order_id BIGINT NOT NULL,
  uid BIGINT NOT NULL,
  address VARCHAR(255) NOT NULL,
  event_type VARCHAR(255) NOT NULL,
  pair_type VARCHAR(255) NOT NULL,
  collateral_type VARCHAR(255) NOT NULL,
  is_long BOOLEAN NOT NULL,
  leverage NUMERIC(38, 20) NOT NULL,
  price NUMERIC(38, 10) NOT NULL,
  original_size NUMERIC(38, 0) NOT NULL,
  size_delta NUMERIC(38, 0) NOT NULL,
  original_collateral NUMERIC(38, 10) NOT NULL,
  collateral_delta NUMERIC(38, 10) NOT NULL,
  is_increase BOOLEAN NOT NULL,
  pnl_without_fee NUMERIC(38, 10) NOT NULL,
  funding_fee NUMERIC(38, 10) NOT NULL,
  rollover_fee NUMERIC(38, 10) NOT NULL,
  entry_exit_fee NUMERIC(38, 10) NOT NULL,
  long_open_interest NUMERIC(38, 10) NOT NULL,
  short_open_interest NUMERIC(38, 10) NOT NULL,
  ts TIMESTAMPTZ NOT NULL
);
`;

export default positionHistoryQuery;

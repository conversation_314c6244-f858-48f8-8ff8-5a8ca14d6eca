import { pgTable, serial, text, integer, index, boolean, json } from 'drizzle-orm/pg-core';
import { timestamps } from '@/db/helpers/column';
import { relations } from 'drizzle-orm';
import { users } from './user';
import { agents, IAgent } from './agent';

export const conversations = pgTable('conversations', {
  id: serial('id').primaryKey(),
  userId: integer('user_id')
    .notNull()
    .references(() => users.id, {
      onDelete: 'cascade',
    }),
  agentId: integer('agent_id')
    .notNull()
    .references(() => agents.id, {
      onDelete: 'cascade',
    }),
  title: text('title'),
  isPublic: boolean('is_public').notNull().default(false),
  ...timestamps,
});

export const conversationsRelations = relations(conversations, ({ one, many }) => ({
  user: one(users, {
    fields: [conversations.userId],
    references: [users.id],
  }),
  agent: one(agents, {
    fields: [conversations.agentId],
    references: [agents.id],
  }),
  chats: many(chats), // All messages in this conversation
}));

export type IConversation = typeof conversations.$inferSelect;

export const chats = pgTable(
  'chats',
  {
    id: serial('id').primaryKey(),
    conversationId: integer('conversation_id')
      .references(() => conversations.id, { onDelete: 'cascade' })
      .notNull(),
    senderId: integer('sender_id').notNull(),
    receiverId: integer('receiver_id').notNull(),
    isAgent: boolean('is_agent').notNull().default(false),
    message: text('message').notNull(),
    messageType: text('message_type').notNull().default('chit_chat'),
    data: json('data').default({}),
    ...timestamps,
  },
  table => [
    index('conversation_idx').on(table.conversationId),
    index('sender_idx').on(table.senderId),
    index('receiver_idx').on(table.receiverId),
  ],
);

export const chatsRelations = relations(chats, ({ one }) => ({
  conversation: one(conversations, {
    fields: [chats.conversationId],
    references: [conversations.id],
  }),
}));

export type IChat = typeof chats.$inferSelect;

export type IConversationWithAgent = IConversation & {
  agent: IAgent;
};

export type IConversationWithAgentAndChats = IConversationWithAgent & {
  chats: IChat[];
};

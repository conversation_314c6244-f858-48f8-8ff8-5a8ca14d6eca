import {
  pgTable,
  serial,
  varchar,
  index,
  integer,
  timestamp,
  numeric,
  pgEnum,
} from 'drizzle-orm/pg-core';
import { relations } from 'drizzle-orm';
import { timestamps } from '../helpers/column';
import { agents } from './agent';
import { conversations } from './chat';

export const users = pgTable(
  'users',
  {
    id: serial('id').primaryKey(),
    address: varchar('address', { length: 255 }).notNull().unique(),
    remainingPrompts: integer('remaining_prompts').default(0),
    ...timestamps,
  },
  table => [index('address_idx').on(table.address)],
);

export const usersRelations = relations(users, ({ one, many }) => ({
  agent: one(agents),
  conversations: many(conversations),
  purchases: many(purchases),
}));

// Purchase table
export const purchases = pgTable(
  'purchases',
  {
    id: serial('id').primaryKey(),
    userId: integer('user_id')
      .notNull()
      .references(() => users.id),
    transactionHash: varchar('transaction_hash', { length: 255 }).notNull().unique(),
    amountPaid: numeric('amount_paid', { precision: 15, scale: 2 }).notNull(),
    boughtCredit: numeric('bought_credit', { precision: 15, scale: 2 }).notNull(),
    ...timestamps,
  },
  table => [
    index('transaction_hash_idx').on(table.transactionHash),
    index('user_id_idx').on(table.userId),
  ],
);

export const purchasesRelations = relations(purchases, ({ one }) => ({
  user: one(users, {
    fields: [purchases.userId],
    references: [users.id],
  }),
}));

// Type inference
export type IUser = typeof users.$inferSelect;
export type IPurchase = typeof purchases.$inferSelect;

import indexerDbPool from '@/config/indexerDb';
import { floorTimeTo } from '@/utils/cron-job/tasks/transactions/utils';

export async function insertAgentActivity(agentActivityData: {
  type: string;
  event_type: string;
  creator_address: string;
  agent_address: string;
  transaction_timestamp: string;
  activity: string;
}): Promise<void> {
  const query = `
        INSERT INTO alura.agent_activity (type, event_type, creator_address, agent_address, transaction_timestamp, activity)
        VALUES ($1, $2, $3, $4, $5, $6)
    `;
  const values = [
    agentActivityData.type,
    agentActivityData.event_type,
    agentActivityData.creator_address,
    agentActivityData.agent_address,
    agentActivityData.transaction_timestamp,
    agentActivityData.activity,
  ];

  await indexerDbPool.query(query, values);
}

interface TradeInput {
  frequency: string;
  closed?: boolean;
  pair: string;
  isLong: boolean;
  collateral: number;
  leverage: number;
  creatorAddress?: string;
  agentAddress: string;
}

export async function insertTrade(input: TradeInput): Promise<void> {
  const {
    frequency,
    closed = false,
    pair,
    isLong,
    collateral,
    leverage,
    creatorAddress,
    agentAddress,
  } = input;

  const now = Date.now();
  const nearestTime = floorTimeTo(now);
  const query = `
      INSERT INTO alura.trades (
        frequency,
        closed,
        pair,
        is_long,
        collateral,
        leverage,
        creator_address,
        agent_address,
        created_at,
        updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
      );
    `;

  const values = [
    frequency,
    closed,
    pair,
    isLong,
    collateral,
    leverage,
    creatorAddress || null,
    agentAddress,
    nearestTime,
    nearestTime,
  ];

  try {
    await indexerDbPool.query(query, values);
  } catch (error) {
    throw error;
  }
}

export async function getTradesToOpen() {
  try {
    const query = `
        SELECT * FROM alura.trades
        WHERE closed = false
      `;
    const result = await indexerDbPool.query(query);
    return result.rows;
  } catch (error) {
    throw error;
  }
}

export async function setUpdatedAtToNow(
  agent_address: string,
  pair: string,
  is_long: boolean,
  frequency: string,
) {
  const now = Date.now();
  const nearestTime = floorTimeTo(now);
  try {
    const query = `
        UPDATE alura.trades
        SET updated_at = $5
        WHERE agent_address = $1 AND pair = $2 AND is_long = $3 AND frequency = $4 AND closed = false
      `;
    const values = [agent_address, pair, is_long, frequency, nearestTime];
    await indexerDbPool.query(query, values);
  } catch (error) {
    throw error;
  }
}

export async function setTradeClosed(agent_address: string, pair: string, is_long: boolean) {
  try {
    const query = `
        UPDATE alura.trades
        SET closed = true
        WHERE agent_address = $1 AND pair = $2 AND is_long = $3 AND closed = false
      `;
    const values = [agent_address, pair, is_long];
    await indexerDbPool.query(query, values);
  } catch (error) {
    throw error;
  }
}

export const getAllLatestPositionHistory = async (
  limit: number = 50,
  cursor?: string, // ISO timestamp string
): Promise<any[]> => {
  const query = `
    SELECT *
    FROM alura.position_history
    ${cursor ? 'WHERE ts > $2' : ''}
    ORDER BY ts DESC
    LIMIT $1;
  `;

  const values = cursor ? [limit, cursor] : [limit];

  try {
    const result = await indexerDbPool.query(query, values);
    return result.rows;
  } catch (error) {
    console.error('Error fetching position history:', error);
    throw error;
  }
};

[{"chainId": 1, "tokenAddress": "0x1::aptos_coin::AptosCoin", "faAddress": "0xa", "name": "Aptos Coin", "symbol": "APT", "decimals": 8, "bridge": null, "panoraSymbol": "APT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/APT.svg", "websiteUrl": "https://aptosfoundation.org", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 1, "coinGeckoId": "aptos", "coinMarketCapId": 21794}, {"chainId": 1, "tokenAddress": null, "faAddress": "0x357b0b74bc833e95a115ad22604854d6b0fca151cecd94111770e5d6ffc9dc2b", "name": "Tether USD", "symbol": "USDt", "decimals": 6, "bridge": null, "panoraSymbol": "USDt", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/USDT.svg", "websiteUrl": "https://tether.to", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 2, "coinGeckoId": "tether", "coinMarketCapId": 825}, {"chainId": 1, "tokenAddress": null, "faAddress": "0xbae207659db88bea0cbead6da0ed00aac12edcdda169e591cd41c94180b46f3b", "name": "USDC", "symbol": "USDC", "decimals": 6, "bridge": null, "panoraSymbol": "USDC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/USDC.svg", "websiteUrl": "https://circle.com/usdc", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 4, "coinGeckoId": "usd-coin", "coinMarketCapId": 3408}, {"chainId": 1, "tokenAddress": "0x5ae6789dd2fec1a9ec9cccfb3acaf12e93d432f0a3a42c92fe1a9d490b7bbc06::mkl_token::MKL", "faAddress": "0x878370592f9129e14b76558689a4b570ad22678111df775befbfcbc9fb3d90ab", "name": "<PERSON><PERSON><PERSON>", "symbol": "MKL", "decimals": 6, "bridge": null, "panoraSymbol": "MKL", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MKL.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 15, "coinGeckoId": "merkle-trade", "coinMarketCapId": 32997}, {"chainId": 1, "tokenAddress": null, "faAddress": "0xb36527754eb54d7ff55daf13bcb54b42b88ec484bd6f0e3b2e0d1db169de6451", "name": "AMNIS", "symbol": "AMI", "decimals": 8, "bridge": null, "panoraSymbol": "AMI", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/AMI.png", "websiteUrl": "https://amnis.finance", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 17, "coinGeckoId": "ami", "coinMarketCapId": 36126}, {"chainId": 1, "tokenAddress": "0x53a30a6e5936c0a4c5140daed34de39d17ca7fcae08f947c02e979cef98a3719::coin::LSD", "faAddress": "0x02370cc1d995f3aadd337c1c6c63834ad8d2bd0cdc70bc8dff81de463e18b159", "name": "Liquidswap", "symbol": "LSD", "decimals": 8, "bridge": null, "panoraSymbol": "LSD", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/LSD.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 20, "coinGeckoId": "pontem-liquidswap", "coinMarketCapId": 22262}, {"chainId": 1, "tokenAddress": "0x7fd500c11216f0fe3095d0c4b8aa4d64a4e2e04f83758462f2b127255643615::thl_coin::THL", "faAddress": "0x377adc4848552eb2ea17259be928001923efe12271fef1667e2b784f04a7cf3a", "name": "<PERSON><PERSON>", "symbol": "THL", "decimals": 8, "bridge": null, "panoraSymbol": "THL", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/THL.svg", "websiteUrl": "https://www.thala.fi", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 40, "coinGeckoId": "thala", "coinMarketCapId": 24268}, {"chainId": 1, "tokenAddress": null, "faAddress": "0x2ebb2ccac5e027a87fa0e2e5f656a3a4238d6a48d93ec9b610d570fc0aa0df12", "name": "Cellana", "symbol": "CELL", "decimals": 8, "bridge": null, "panoraSymbol": "CELL", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/CELL.png", "websiteUrl": "https://cellana.finance", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 45, "coinGeckoId": "cellena-finance", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": null, "faAddress": "0xeedba439a4ab8987a995cf5cfefebd713000b3365718a29dfbc36bc214445fb8", "name": "VibrantX token", "symbol": "VIBE", "decimals": 8, "bridge": null, "panoraSymbol": "VIBE", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/VIBE.png", "websiteUrl": "https://vibrantx.finance", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 47, "coinGeckoId": null, "coinMarketCapId": 35266}, {"chainId": 1, "tokenAddress": "0xe4ccb6d39136469f376242c31b34d10515c8eaaa38092f804db8e08a8f53c5b2::assets_v1::EchoCoin002", "faAddress": "0x0009da434d9b873b5159e8eeed70202ad22dc075867a7793234fbc981b63e119", "name": "<PERSON><PERSON>", "symbol": "GUI", "decimals": 6, "bridge": null, "panoraSymbol": "GUI", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/GUI.png", "websiteUrl": "https://www.guiinu.com", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 50, "coinGeckoId": "gui-inu", "coinMarketCapId": 28851}, {"chainId": 1, "tokenAddress": "0x9d0595765a31f8d56e1d2aafc4d6c76f283c67a074ef8812d8c31bd8252ac2c3::asset::TOMA", "faAddress": "0x79e8a5ddb82aa53854c1348c2865fd00732a41937dc1c160a4a50205537bd740", "name": "Tomarket", "symbol": "TOMA", "decimals": 6, "bridge": null, "panoraSymbol": "TOMA", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/TOMA.png", "websiteUrl": "https://tomarket.ai", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 51, "coinGeckoId": "tomarket", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": null, "faAddress": "0xd0ab8c2f76cd640455db56ca758a9766a966c88f77920347aac1719edab1df5e", "name": "Amaterasu", "symbol": "AMA", "decimals": 8, "bridge": null, "panoraSymbol": "AMA", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/AMA.png", "websiteUrl": "https://amaterasu.gg", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 52, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x73eb84966be67e4697fc5ae75173ca6c35089e802650f75422ab49a8729704ec::coin::<PERSON><PERSON>oo", "faAddress": "0xb27b0c6b60772f0fc804ec1cd3339f552badf9bd1e125a7dd700d8eb11248ef1", "name": "Doo<PERSON>oo", "symbol": "Doo<PERSON>oo", "decimals": 8, "bridge": null, "panoraSymbol": "Doo<PERSON>oo", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/DOODOO.png", "websiteUrl": "https://doodoo.io", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 60, "coinGeckoId": "doodoo", "coinMarketCapId": 28881}, {"chainId": 1, "tokenAddress": "0x268d4a7a2ad93274edf6116f9f20ad8455223a7ab5fc73154f687e7dbc3e3ec6::LOON::LOON", "faAddress": "0x41dfe1fb3d33d4d9d0b460f03ce1c0a6af6520dd8bdc0f204583c4987faf81de", "name": "The Loonies", "symbol": "LOON", "decimals": 6, "bridge": null, "panoraSymbol": "LOON", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/LOON.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 65, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": null, "faAddress": "0xf37a8864fe737eb8ec2c2931047047cbaed1beed3fb0e5b7c5526dafd3b9c2e9", "name": "USDe", "symbol": "USDe", "decimals": 6, "bridge": null, "panoraSymbol": "USDe", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/USDe.png", "websiteUrl": "https://ethena.fi", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 67, "coinGeckoId": "ethena-usde", "coinMarketCapId": 29470}, {"chainId": 1, "tokenAddress": "0x111ae3e5bc816a5e63c2da97d0aa3886519e0cd5e4b046659fa35796bd11542a::amapt_token::AmnisApt", "faAddress": "0xa259be733b6a759909f92815927fa213904df6540519568692caf0b068fe8e62", "name": "Amnis Aptos Coin", "symbol": "amAPT", "decimals": 8, "bridge": null, "panoraSymbol": "amAPT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/amAPT.png", "websiteUrl": "https://amnis.finance", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 70, "coinGeckoId": "amnis-aptos", "coinMarketCapId": 29034}, {"chainId": 1, "tokenAddress": "0x111ae3e5bc816a5e63c2da97d0aa3886519e0cd5e4b046659fa35796bd11542a::stapt_token::StakedApt", "faAddress": "0xb614bfdf9edc39b330bbf9c3c5bcd0473eee2f6d4e21748629cc367869ece627", "name": "Staked Aptos Coin", "symbol": "stAPT", "decimals": 8, "bridge": null, "panoraSymbol": "stAPT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/stAptAmnis.svg", "websiteUrl": "https://stake.amnis.finance", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 75, "coinGeckoId": "amnis-staked-aptos-coin", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xfaf4e633ae9eb31366c9ca24214231760926576c7b625313b3688b5e900731f6::staking::ThalaAPT", "faAddress": "0xa0d9d647c5737a5aed08d2cfeb39c31cf901d44bc4aa024eaa7e5e68b804e011", "name": "Thala APT", "symbol": "thAPT", "decimals": 8, "bridge": null, "panoraSymbol": "thAPT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/THAPT.png", "websiteUrl": "https://thala.fi", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 80, "coinGeckoId": "thala-apt", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xfaf4e633ae9eb31366c9ca24214231760926576c7b625313b3688b5e900731f6::staking::StakedThalaAPT", "faAddress": "0x0a9ce1bddf93b074697ec5e483bc5050bc64cff2acd31e1ccfd8ac8cae5e4abe", "name": "Staked Thala APT", "symbol": "sthAPT", "decimals": 8, "bridge": null, "panoraSymbol": "sthAPT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/STHAPT.png", "websiteUrl": "https://thala.fi", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 85, "coinGeckoId": "staked-thala-apt", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x6f986d146e4a90b828d8c12c14b6f4e003fdff11a8eecceceb63744363eaac01::mod_coin::MOD", "faAddress": "0x94ed76d3d66cb0b6e7a3ab81acf830e3a50b8ae3cfb9edc0abea635a11185ff4", "name": "Move Dollar", "symbol": "MOD", "decimals": 8, "bridge": null, "panoraSymbol": "MOD", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MOD.svg", "websiteUrl": "https://thala.fi", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 90, "coinGeckoId": "move-dollar", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::USDC", "faAddress": "0x2b3be0a97a73c87ff62cbdd36837a9fb5bbd1d7f06a73b7ed62ec15c5326c1b8", "name": "USD Coin", "symbol": "USDC", "decimals": 6, "bridge": "LayerZero", "panoraSymbol": "lzUSDC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/lzUSDC.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 91, "coinGeckoId": "layerzero-bridged-usdc-aptos", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::USDT", "faAddress": "0xe568e9322107a5c9ba4cbd05a630a5586aa73e744ada246c3efb0f4ce3e295f3", "name": "Tether USD", "symbol": "USDT", "decimals": 6, "bridge": "LayerZero", "panoraSymbol": "lzUSDT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/lzUSDT.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 93, "coinGeckoId": "layerzero-bridged-usdt-aptos", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::WETH", "faAddress": "0xae02f68520afd221a5cd6fda6f5500afedab8d0a2e19a916d6d8bc2b36e758db", "name": "Wrapped Ether", "symbol": "WETH", "decimals": 6, "bridge": "LayerZero", "panoraSymbol": "lzWETH", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/lzWETH.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 94, "coinGeckoId": "layerzero-bridged-weth-aptos", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x5e156f1207d0ebfa19a9eeff00d62a282278fb8719f4fab3a586a0a2c0fffbea::coin::T", "faAddress": "0x54fc0d5fa5ad975ede1bf8b1c892ae018745a1afd4a4da9b70bb6e5448509fc0", "name": "USD Coin", "symbol": "USDC", "decimals": 6, "bridge": "Wormhole", "panoraSymbol": "whUSDC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whUSDC.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 95, "coinGeckoId": "bridged-usd-coin-wormhole-ethereum", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": null, "faAddress": "0x8a7403ae3d95f181761cf36742680442c698b49e047350b77a8906ec5168bdae", "name": "eAPT", "symbol": "eAPT", "decimals": 8, "bridge": null, "panoraSymbol": "eAPT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/eAPT.svg", "websiteUrl": "https://www.echo-protocol.xyz", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 96, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x4e1854f6d332c9525e258fb6e66f84b6af8aba687bbcb832a24768c4e175feec::abtc::ABTC", "faAddress": "0xf599112bc3a5b6092469890d6a2f353f485a6193c9d36626b480704467d3f4c8", "name": "aBTC", "symbol": "aBTC", "decimals": 10, "bridge": "Echo", "panoraSymbol": "aBTC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/aBTC.svg", "websiteUrl": "https://www.echo-protocol.xyz", "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 97, "coinGeckoId": "abtc", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": null, "faAddress": "0x92f0951cecb35dc7613bc0e09f27e2c7aaebdc320f93901c121a1ec06f3aa455", "name": "TEVI Coin", "symbol": "TEVI", "decimals": 8, "bridge": null, "panoraSymbol": "TEVI", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/TEVI.svg", "websiteUrl": "https://web3.tevi.com", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 98, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": null, "faAddress": "0xc9a5d270bb8bb47e7bb34377ceb529db2878e4a7b521b5b8a984b35f8feaa8e2", "name": "<PERSON><PERSON><PERSON>", "symbol": "RWD", "decimals": 8, "bridge": null, "panoraSymbol": "RWD", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/RWD.png", "websiteUrl": "https://www.rewardywallet.com", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 99, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x4fbed3f8a3fd8a11081c8b6392152a8b0cb14d70d0414586f0c9b858fcd2d6a7::UPTOS::UPTOS", "faAddress": "0x5915ae0eae3701833fa02e28bf530bc01ca96a5f010ac8deecb14c7a92661368", "name": "UPTOS", "symbol": "UPTOS", "decimals": 8, "bridge": null, "panoraSymbol": "UPTOS", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/UPTOS.svg", "websiteUrl": "https://uptos.xyz", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 100, "coinGeckoId": "uptos", "coinMarketCapId": 30366}, {"chainId": 1, "tokenAddress": "0xc26a8eda1c3ab69a157815183ddda88c89d6758ee491dd1647a70af2907ce074::coin::Chewy", "faAddress": "0x1fe81b3886ff129d42064f7ee934013de7ef968cb8f47adb5f7210192bcd4a23", "name": "Chewy", "symbol": "CHEWY", "decimals": 0, "bridge": null, "panoraSymbol": "CHEWY", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/CHEWY.png", "websiteUrl": "https://chewytoken.com", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 105, "coinGeckoId": "chewy-token", "coinMarketCapId": 30840}, {"chainId": 1, "tokenAddress": "0xc5fbbcc4637aeebb4e732767abee8a21f2b0776f73b73e16ce13e7d31d6700da::MOOMOO::MOOMOO", "faAddress": "0xa0fa5918da73235921c6120597db820df0be391d0056dc0a7ee7a80b83f29d64", "name": "MOO MOO", "symbol": "MOOMOO", "decimals": 6, "bridge": null, "panoraSymbol": "MOOMOO", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MOOMOO.png", "websiteUrl": "https://www.moo.meme", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 107, "coinGeckoId": "moo-moo", "coinMarketCapId": 34379}, {"chainId": 1, "tokenAddress": "0x5e975e7f36f2658d4cf146142899c659464a3e0d90f0f4d5f8b2447173c06ef6::EDOG::EDOG", "faAddress": "0x1ff8bf54987b665fd0aa8b317a22a60f5927675d35021473a85d720e254ed77e", "name": "EDOG", "symbol": "EDOG", "decimals": 6, "bridge": null, "panoraSymbol": "EDOG", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/EDOG.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 108, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x8ea59d57259d0312fa21e0cb9099d359462d9e0050c9139960ff9a2313ce1c9d::coin::T", "faAddress": "0xdc31ffdf65279c560ca6ede280d0359889050687388ffbb3fb25c7d327cfa4e6", "name": "o.xyz", "symbol": "O", "decimals": 8, "bridge": null, "panoraSymbol": "O", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/o-xyz.svg", "websiteUrl": "https://www.o.xyz/", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 200000, "coinGeckoId": "o-xyz", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x8512b34017e087c3707748869ddc317d83f3fe70ab3a162abdc055c761ca9906::OBOT::OBOT", "faAddress": "0xa491095e3906fbca27b6fb7345d4ea407282c1aecbd50b2d2a42b1fa977bae30", "name": "OBOT", "symbol": "OBOT", "decimals": 8, "bridge": null, "panoraSymbol": "OBOT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/OBOT.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": "obot", "coinMarketCapId": 33986}, {"chainId": 1, "tokenAddress": "0x63be1898a424616367e19bbd881f456a78470e123e2770b5b5dcdceb61279c54::movegpt_token::MovegptCoin", "faAddress": "0x4c3efb98d8d3662352f331b3465c6df263d1a7e84f002844348519614a5fea30", "name": "MOVEGPT", "symbol": "MGPT", "decimals": 8, "bridge": null, "panoraSymbol": "MGPT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MGPT.png", "websiteUrl": "https://movegpt.io/", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 115, "coinGeckoId": "movegpt", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xe88ae9670071da40a9a6b1d97aab8f6f1898fdc3b8f1c1038b492dfad738448b::coin::Donk", "faAddress": "0x41944cf1d4dac152d692644944e2cc49ee81fafdfb37abd541d06388ec3f7eda", "name": "Donk", "symbol": "DONK", "decimals": 8, "bridge": null, "panoraSymbol": "DONK", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/DONK.png", "websiteUrl": "https://donkmove.com/", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 120, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x96baeee6d7a4a8cd712144d1225cfcb6c26d0c6fefd463bd77a878e4526c7411::hair_coin::Hair<PERSON><PERSON>n", "faAddress": "0xd08a1ab00895c35dd19b356f5747355ebcd58cf5e684c15e6808d760ffd6beff", "name": "Hair", "symbol": "HAIR", "decimals": 8, "bridge": null, "panoraSymbol": "HAIR", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/HAIR.png", "websiteUrl": "https://hairclub.io", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 122, "coinGeckoId": "hair", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x2d4de7378c573dadc2e589892d709ee24f3c26f23b57804f384f4803da2e6f0a::ZIPO::ZIPO", "faAddress": "0x76dd1fafe2f28dd1af8e71a02a41fb96d1f63b88bebdc629bbfe76703f5e7a6a", "name": "ZIPPO THE OG MASCOT", "symbol": "ZIPO", "decimals": 6, "bridge": null, "panoraSymbol": "ZIPO", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ZIPO.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 125, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x159df6b7689437016108a019fd5bef736bac692b6d4a1f10c941f6fbb9a74ca6::oft::CakeOFT", "faAddress": "0xad18575b0e51dd056e1e082223c0e014cbfe4b13bc55e92f450585884f4cf951", "name": "PancakeSwap Token", "symbol": "CAKE", "decimals": 8, "bridge": null, "panoraSymbol": "CAKE", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/Pancake.png", "websiteUrl": "https://pancakeswap.finance", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 130, "coinGeckoId": "pancakeswap-token", "coinMarketCapId": 7186}, {"chainId": 1, "tokenAddress": null, "faAddress": "0xaef6a8c3182e076db72d64324617114cacf9a52f28325edc10b483f7f05da0e7", "name": "TruAPT coin", "symbol": "TruAPT", "decimals": 8, "bridge": null, "panoraSymbol": "TruAPT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/TruAPT.png", "websiteUrl": "https://www.trufin.io", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 135, "coinGeckoId": "trufin-staked-apt", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": null, "faAddress": "0xe9c6ae7a056ba49901fcc19ab3fcff0938f882cfd7f2cc5a72eea362d29f5b8f", "name": "BAPTMAN", "symbol": "BAPTMAN", "decimals": 9, "bridge": null, "panoraSymbol": "BAPTMAN", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/BAPTMAN.png", "websiteUrl": "https://baptmantoken.com", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 140, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::WBTC", "faAddress": "0xa64d2d6f5e26daf6a3552f51d4110343b1a8c8046d0a9e72fa4086a337f3236c", "name": "Wrapped BTC", "symbol": "WBTC", "decimals": 6, "bridge": "LayerZero", "panoraSymbol": "lzWBTC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/lzWBTC.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 145, "coinGeckoId": "layerzero-bridged-wbtc-aptos", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x27fafcc4e39daac97556af8a803dbb52bcb03f0821898dc845ac54225b9793eb::move_coin::MoveCoin", "faAddress": "0x96d1ccca420ebc20fc8af6cacb864e44856ca879c6436d4e9be2b0a4b99bf852", "name": "BlueMove", "symbol": "MOVE", "decimals": 8, "bridge": null, "panoraSymbol": "MOVE", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MOVE.svg", "websiteUrl": "https://bluemove.net", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 150, "coinGeckoId": "bluemove", "coinMarketCapId": 23359}, {"chainId": 1, "tokenAddress": "0xe234f0e7c05165cc48f5407c3eb542709a8284fb6b9d66068413a2e13ef423bd::MOVEPUMP::MOVEPUMP", "faAddress": "0xa067de5ce739da1400a92945646f719a0265df6412ddab872cd670052c5cc12f", "name": "MovePump", "symbol": "MOVEPUMP", "decimals": 6, "bridge": null, "panoraSymbol": "MOVEPUMP", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MOVEPUMP.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 155, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xa2eda21a58856fda86451436513b867c97eecb4ba099da5775520e0f7492e852::coin::T", "faAddress": "0x6704464238d73a679486420aab91a8a2a01feb9d700e8ba3332aa3e41d3eab62", "name": "Tether USD", "symbol": "USDT", "decimals": 6, "bridge": "Wormhole", "panoraSymbol": "whUSDT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whUSDT.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 160, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xada35ada7e43e2ee1c39633ffccec38b76ce702b4efc2e60b50f63fbe4f710d8::apetos_token::ApetosCoin", "faAddress": "0xfad230e7d9df2baf83a68b6f50217ed3c06da593e766970a885965b43b894a04", "name": "APETOS", "symbol": "APE", "decimals": 8, "bridge": null, "panoraSymbol": "APE", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/APE.png", "websiteUrl": "https://apetos.xyz/", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 165, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xd6a49762f6e4f7401ee79be6f5d4111e70db1408966ba1aa204e6e10c9d437ca::bubbles::BubblesCoin", "faAddress": "0xaca80bdba3a9f58af0c6348f15530e4d891d1c60abca4c2cfb4c1a73bff0f8dd", "name": "BUBBLES", "symbol": "BUBBLES", "decimals": 8, "bridge": null, "panoraSymbol": "BUBBLES", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/BUBBLES.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 170, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xae478ff7d83ed072dbc5e264250e67ef58f57c99d89b447efd8a0a2e8b2be76e::coin::T", "faAddress": "0x4a884be56ef4c11a27162bf30b32e8e3615dcb3df0fc1777c8eb69c1991f34d0", "name": "Wrapped BTC", "symbol": "WBTC", "decimals": 8, "bridge": "Wormhole", "panoraSymbol": "whWBTC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whWBTC.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 175, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xdcfa079344261bfde45e7f6281df091743b8d3098bf9e26e1c0212fc5b070621::zaaptos_token::ZaaptosCoin", "faAddress": "0xcd70630fb90cab716ab01a7884821f86dceb1bbb09a89683b5c22c5462503f51", "name": "Zaaptos Coin", "symbol": "ZAAP", "decimals": 8, "bridge": null, "panoraSymbol": "ZAAP", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ZAAP.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 180, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x4c71c33b9ec2f263a7f25779bd9a2531165d4da9a963e042a20f288688094a66::APTO_THE_MASCOT::APTO_THE_MASCOT", "faAddress": "0x5e438070c801e45c89528d1217e019178a3211036250fed988338f43f891ca31", "name": "Apto The Mascot", "symbol": "APTO", "decimals": 8, "bridge": null, "panoraSymbol": "APTO", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/APTO.png", "websiteUrl": "https://aptothemascot.com/", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 185, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x61ed8b048636516b4eaf4c74250fa4f9440d9c3e163d96aeb863fe658a4bdc67::CASH::CASH", "faAddress": "0xc692943f7b340f02191c5de8dac2f827e0b66b3ed2206206a3526bcb0cae6e40", "name": "CASH", "symbol": "CASH", "decimals": 6, "bridge": null, "panoraSymbol": "CASH", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/CASH.png", "websiteUrl": "https://www.cash.markets/", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 190, "coinGeckoId": "cash-2", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": null, "faAddress": "0xb30a694a344edee467d9f82330bbe7c3b89f440a1ecd2da1f3bca266560fce69", "name": "Staked USDe", "symbol": "sUSDe", "decimals": 6, "bridge": null, "panoraSymbol": "sUSDe", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/sUSDe.png", "websiteUrl": "https://ethena.fi", "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 194, "coinGeckoId": "ethena-staked-usde", "coinMarketCapId": 29471}, {"chainId": 1, "tokenAddress": "0xcc8a89c8dce9693d354449f1f73e60e14e347417854f029db5bc8e7454008abb::coin::T", "faAddress": "0x92410a41654236295001f06375afbb1786dbd14bc1c42a33bfcf50204c248bb7", "name": "Wrapped Ether", "symbol": "WETH", "decimals": 8, "bridge": "Wormhole", "panoraSymbol": "whWETH", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whWETH.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 195, "coinGeckoId": "ethereum-wormhole", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x543c5660aa4d496687e2068c11765f04607c4f4b639a83233a9333604fb8ce59::stakestone_ether::StakeStoneEther", "faAddress": "0x700e285ee9f4fc9b0e42a6217e329899e1353476bc532a484048008c8bc8e400", "name": "StakeStone Ether", "symbol": "STONE", "decimals": 8, "bridge": "LayerZero", "panoraSymbol": "lzSTONE", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/lzSTONE.png", "websiteUrl": "https://stakestone.io", "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 200, "coinGeckoId": "stakestone-ether", "coinMarketCapId": 32194}, {"chainId": 1, "tokenAddress": "0x5dee1d4b13fae338a1e1780f9ad2709a010e824388efd169171a26e3ea9029bb::stakestone_bitcoin::StakeStoneBitcoin", "faAddress": "0xef1f3c4126176b1aaff3bf0d460a9344b64ac4bd28ff3e53793d49ded5c2d42f", "name": "StakeStone Bitcoin", "symbol": "SBTC", "decimals": 8, "bridge": "LayerZero", "panoraSymbol": "lzSBTC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/lzSBTC.png", "websiteUrl": "https://stakestone.io", "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 201, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": null, "faAddress": "0xef0d49f03e48dbd055c3a369f74a304c366bda148005ddf6bb881ced79da0b09", "name": "Night Coin", "symbol": "NIGHT", "decimals": 8, "bridge": null, "panoraSymbol": "NIGHT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/NIGHT.png", "websiteUrl": "https://midnight.io", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 210, "coinGeckoId": null, "coinMarketCapId": 34135}, {"chainId": 1, "tokenAddress": "0x6f446fe32a361c5512863d5e610f7d7eaa54d5cee1cea6a2712f2e56da693f1c::MODENG::MODENG", "faAddress": "0x5dd9b0b5eea4d2bec9c8cabed2f1db7062fb6ed16471fd52de491426718b0d95", "name": "<PERSON>", "symbol": "MODENG", "decimals": 6, "bridge": null, "panoraSymbol": "MODENG", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MODENG.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 299, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x9d4d8a8f1551317276bc2fbf924e7adf82b722b72f4f914dcfb6cc1811bc383f::Sidelined::Sidelined", "faAddress": "0x073992b487d517a8fc710acf953248b2045e381e5eb6fd9a92828db64a269530", "name": "sidelined?", "symbol": "Sidelined", "decimals": 6, "bridge": null, "panoraSymbol": "Sidelined", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/SIDELINED.png", "websiteUrl": "https://x.com/SidelinedAPT", "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 300, "coinGeckoId": null, "coinMarketCapId": 35898}, {"chainId": 1, "tokenAddress": "0x42d77150661adcc068603bde2453bea1e22fa7ca08878ec88b7e077709c01171::oft::WUSDMOFT", "faAddress": "0x08993fd61f611eb186386962edec50c9d72532b3825f1c7d98e883268dbbc501", "name": "Wrap USDM", "symbol": "WUSDM", "decimals": 8, "bridge": "LayerZero", "panoraSymbol": "lzWUSDM", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/lzWUSDM.png", "websiteUrl": "https://mountainprotocol.com", "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 301, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x4ef6d6d174ae393cec4c8af0b75638082fe45c92e552b4df8bc679e3a0ddcb13::CAPTOS::CAPTOS", "faAddress": "0x7fa78d58cccc849363df4ed1acd373b1f09397d1c322450101e3b0a4a7a14d80", "name": "<PERSON><PERSON>", "symbol": "CAPTOS", "decimals": 6, "bridge": null, "panoraSymbol": "CAPTOS", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/CAPTOS.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 302, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x24dc0c6bd0a98f31961589e6432a8038833128f7c802ba148172553987c379f::MEOW::MEOW", "faAddress": "0xd8b8cd2c1a87374567a0fe48c6c785237921e09395fbbf3f4754a39cc61b3270", "name": "<PERSON><PERSON><PERSON>", "symbol": "MEOW", "decimals": 6, "bridge": null, "panoraSymbol": "MEOW", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MEOW.png", "websiteUrl": "https://meowtos.xyz", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 303, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x33df6ad8f2208d8f575076e59fda6e195ef5c1908f0dae9e461a1aada7c12418::SNZ::SNZ", "faAddress": "0x65feece1d9f9f495325404a9b022184040479ffaf00ddba8a17d6f49d0fe8f52", "name": "Tissue", "symbol": "SNZ", "decimals": 6, "bridge": null, "panoraSymbol": "SNZ", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/SNZ.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x967adbf2e05fe665ab86a3bf2c4acfa39fbf62097963474ef70a0786dae8cfa2::NRUH::NRUH", "faAddress": "0x78d37cff9f42109ce68cd73edb9ef24bb03aad697d7b6449a544701e425befbf", "name": "nRuH BeRs", "symbol": "NRUH", "decimals": 6, "bridge": null, "panoraSymbol": "NRUH", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/NRUH.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xaf1077397a61aebda08ff61dfebd571ccdf7a777618b8d66832b5ab0656852bd::FOG::FOG", "faAddress": "0x297f6d62b7816181dab5e799f86640e51fe03c1e485ce6c72d111142616f4e3a", "name": "FAG DOG COIN", "symbol": "FOG", "decimals": 6, "bridge": null, "panoraSymbol": "FOG", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/FOG.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x23906ec47702dfcdb8ca35dc6eab5cb5d567b86ac1ef0d32a53b786c14ed0d98::POLES::POLES", "faAddress": "0xa8be44f83eb76f7ddcd58bd69e9d3775246e48b0a981511c610726ed3d25c217", "name": "CHOADpoles", "symbol": "POLES", "decimals": 6, "bridge": null, "panoraSymbol": "POLES", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/POLES.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x8f59ac670c9cdaa22503bfede0be49e6759626b18727fbf8d876e18861ff3986::RAPT::RAPT", "faAddress": "0x67941a4189ae5f8037d9532526029bf12dec1b1875a8c9b03034686240fdda41", "name": "<PERSON><PERSON>", "symbol": "RAPT", "decimals": 6, "bridge": null, "panoraSymbol": "RAPT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/RAPT.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xbafd66d813a51ed6b5d2db99b18b61d2fccbc2e46979690fe84198272c59ca5f::zaptos::Zaptos", "faAddress": "0xf0bf190ad68b691f597d00426d0c5fc7d29c5fd2402b3c47b68ba868b06571fb", "name": "ZAPTOS", "symbol": "ZAPT", "decimals": 8, "bridge": null, "panoraSymbol": "ZAPT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ZAPT.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x2ac1304f147af6f3e3362c64f1c9c95dd84f67a8d6e300185e22c5a6f8f661af::PUPTOS::PUPTOS", "faAddress": "0xe9c9d57c025b6e66defca170be1838516dfb413e44a2902479621f987ea95917", "name": "PUPTOS", "symbol": "PUPTOS", "decimals": 6, "bridge": null, "panoraSymbol": "PUPTOS", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/PUPTOS.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x8e36c188da866c4faccb1ace68a33cca40149d942953a6b6a9976e709396ddb8::BEARD::BEARD", "faAddress": "0x2fe25d855c687221bb23b5fe83bb2bd703e9e83241c1a554f94128864182a730", "name": "$BEARD", "symbol": "BEARD", "decimals": 6, "bridge": null, "panoraSymbol": "BEARD", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/BEARD.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xf9cd51e1211f467c52d91c96491aae6c7a068e5f23736e2b58f885412346624c::PP1::PP", "faAddress": "0x7532610fb3a8bbcac22e42a3d625ec9968746a7600a3001870d32cee4e32e329", "name": "PP token", "symbol": "PP", "decimals": 6, "bridge": null, "panoraSymbol": "PP", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/PP.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xbd92f54e3ebf1e80a0efa05f8bbd607fc6dd00ec208c0d743ee6ba3c10f03846::APOP::APOP", "faAddress": "0xb3bbb3f4cc3709c46d9599e72a865a63476eac4c990d84d193f82198969c3696", "name": "Apeople Coin", "symbol": "APOP", "decimals": 6, "bridge": null, "panoraSymbol": "APOP", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/APOP.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x2eebec013472480cc903cf7f118ccadcf813b4bf5e57aafba250746eff2150d5::BEDOG::BEDOG", "faAddress": "0x22b8228d8f128e901bf17b6155f2979acebb26d2a621af61244b8494868bfe4d", "name": "Baby <PERSON>", "symbol": "BEDOG", "decimals": 6, "bridge": null, "panoraSymbol": "BEDOG", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/BEDOG.svg", "websiteUrl": "https://edog.baby", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x55c833f3c668dd863d3848d306e426a33eac615bb93be03fe3a2f721701fec1::UPDOG::UPDOG", "faAddress": "0x77289675e2cc92b135294281fb232735b654fc6e901781cd8b55a9a69bdaa425", "name": "UPDOG", "symbol": "UPDOG", "decimals": 6, "bridge": null, "panoraSymbol": "UPDOG", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/UPDOG.png", "websiteUrl": "https://updogapt.xyz", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x1605f6d99b995e8ddb6200fdccccf97a2497fe1f024a46f6c178a501f0d1b60e::ACAT::ACAT", "faAddress": "0x559d0c5ae1730bcb6804b8b71e92db5872c4d5e48a8677d3e9379ac0d2de8f9f", "name": "AptoCat", "symbol": "ACAT", "decimals": 6, "bridge": null, "panoraSymbol": "ACAT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ACAT.png", "websiteUrl": "https://www.aptocat.xyz", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xc95e07a20f94acb04f1188b51bafbbeec93363760c066f4ced3884a48137da8a::GHO::GHO", "faAddress": "0x70740db1fddae309f1a3612091446fc7db1f278a8df30f28c3871a624e14967a", "name": "GHOST", "symbol": "GHO", "decimals": 6, "bridge": null, "panoraSymbol": "GHO", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/GHO.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x135726730c0e4e71652a6726354f148dec46974d5c152b0042ad58b37ea3c864::FaptOS::FaptOS", "faAddress": "0x6ff3ab54e5052a48a9865eb9578ba8eae6640c0f6c263f752cbdee0cc24bd9f3", "name": "FaptOS", "symbol": "FaptOS", "decimals": 6, "bridge": null, "panoraSymbol": "FaptOS", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/FAPTOS.png", "websiteUrl": "https://faptosaptos.com", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xce1a44fa48a726258a11a499797bc38befa6bc2ef4019b686c59e0a301b1a1c8::BUBU::BUBU", "faAddress": "0x91d2a5e0fa8b93d3317408c877d8c6490b1f4b3baf9a988003ad760a77c1e1e7", "name": "LABUBUTOS", "symbol": "BUBU", "decimals": 6, "bridge": null, "panoraSymbol": "BUBU", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/BUBU.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xbafe0208ba2a3ea2c66a6868f9361a13f4014490e9b77fb0364c5cf55340ffbe::AMAGA::AMAGA", "faAddress": "0x08f0221229fce7f51120a02a31bd2a349b349b17d05521f1e2040708a2c054e0", "name": "AptosMAGA", "symbol": "AMAGA", "decimals": 6, "bridge": null, "panoraSymbol": "AMAGA", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/AMAGA.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x68495e47585861928e07e1fc629501b06822b230508bcf05c4fc19bec9a82405::MOTOS::MOTOS", "faAddress": "0x7dfa992a2e8f8ff61b3fb4d520bd83b5e0c35e412ec049fb887f5b4e2f6a9b55", "name": "MOTOS", "symbol": "MOTOS", "decimals": 6, "bridge": null, "panoraSymbol": "MOTOS", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MOTOS.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xa8c3d4ec01aa0bcaa23bac67d0c3e9d5df2d9e8b82a01ad4f8448aebf3d9a961::POK::POK", "faAddress": "0x9b23312ac7da341796c574f0e50d5d7ad666d3811b2dfbcb60141a2e346936ba", "name": "Purrfectly Okay Coin", "symbol": "POK", "decimals": 6, "bridge": null, "panoraSymbol": "POK", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/POK.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x744467880fbc1a723d8ba8437cd6de3de942789fe6611fc992de8574596c9b5c::CMT::CMT", "faAddress": "0x60325dce3573382050cdb9da7ab509093013c1c240cc61f63af83bc8f0f0ff19", "name": "Cryptomolot", "symbol": "CMT", "decimals": 6, "bridge": null, "panoraSymbol": "CMT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/CMT.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x2f277e648b3761b65818d92f0a7c151cb38a1d33b1a8b18d4c5748bbcd9fcca5::Netzy::Netzy", "faAddress": "0x958886d1500861e49e334be98bad328f977821d3d25ef6eeeb3858a75ad0e1c4", "name": "The Mole Netzy", "symbol": "<PERSON><PERSON>", "decimals": 6, "bridge": null, "panoraSymbol": "<PERSON><PERSON>", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/NETZY.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x541e21bc602730511c0b2de1dbb8e77e5e16031931a955dc988bbed3f600b510::Rice::Rice", "faAddress": "0x87cc249672359d7bf6c71d8b8512e45280c086c181c7b3c37fa625b97c6f47b6", "name": "Rice Token", "symbol": "Rice", "decimals": 6, "bridge": null, "panoraSymbol": "Rice", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/RICE.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xc89f2e0a7f61424e0b951936820f0423e3f076948dcdced112b76244ed840a25::burritos_coin::BurritosCoin", "faAddress": "0xbe31ddc0e4ca8d37106e5d5b02bfba36a5fc5f301af5acd8f8c9063fa774eec8", "name": "BURRITOS", "symbol": "BURRITOS", "decimals": 9, "bridge": null, "panoraSymbol": "BURRITOS", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/BURRITOS.png", "websiteUrl": "https://burritos-aptos.com", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xd11107bdf0d6d7040c6c0bfbdecb6545191fdf13e8d8d259952f53e1713f61b5::staked_coin::StakedAptos", "faAddress": "0xa743351ed4889845737082ab9fcd42c21270647e2c6f342c509320e974035ed2", "name": "<PERSON><PERSON>", "symbol": "stAPT", "decimals": 8, "bridge": null, "panoraSymbol": "dstAPT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/stAptDitto.png", "websiteUrl": "https://www.dittofinance.io", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 200000, "coinGeckoId": "ditto-staked-aptos", "coinMarketCapId": 22290}, {"chainId": 1, "tokenAddress": "0x84d7aeef42d38a5ffc3ccef853e1b82e4958659d16a7de736a29c55fbbeb0114::staked_aptos_coin::StakedAptosCoin", "faAddress": "0x1912eb1a5f8f0d4c72c1687eb199b7f9d2df34da5931ec96830c557f7abaa0ad", "name": "Tortuga Staked Aptos", "symbol": "tAPT", "decimals": 8, "bridge": null, "panoraSymbol": "tAPT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/tAptTortuga.png", "websiteUrl": "https://tortuga.finance", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 200000, "coinGeckoId": "tortuga-staked-aptos", "coinMarketCapId": 22412}, {"chainId": 1, "tokenAddress": "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::<PERSON>dtCoin", "faAddress": "0xc448a48da1ed6f6f378bb82ece996be8b5fc8dd1ea851ea2c023de17714dd747", "name": "Tether USD", "symbol": "USDT", "decimals": 6, "bridge": "<PERSON><PERSON>", "panoraSymbol": "ceUSDT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ceUSDT.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 329, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x2dcbc03740a6fa2efee926b9df329184cce357d0573bdab09930f4d48e61a4c8::STOS::STOS", "faAddress": "0xeb73df9d3ba3fbc2538d2e7f4a2dac9718b48b07f65596a9c7cc84d978e3d6cd", "name": "<PERSON><PERSON><PERSON>", "symbol": "STOS", "decimals": 6, "bridge": null, "panoraSymbol": "STOS", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/STOS.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xe9c192ff55cffab3963c695cff6dbf9dad6aff2bb5ac19a6415cad26a81860d9::mee_coin::Mee<PERSON><PERSON>n", "faAddress": "0x8ad67293fad95a374c7b54f89e8f6dc672bc980a3d6b700900406806ac2c6dd8", "name": "<PERSON><PERSON>", "symbol": "MEE", "decimals": 6, "bridge": null, "panoraSymbol": "MEE", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MEE.svg", "websiteUrl": "https://meeiro.xyz", "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": 22705}, {"chainId": 1, "tokenAddress": "0xfbab9fb68bd2103925317b6a540baa20087b1e7a7a4eb90badee04abb6b5a16f::blt::Blt", "faAddress": "0x4d6f2761961e3a4edd442bd0c3eb692adc256cd5f52f2ebb394a517f3d885c7d", "name": "<PERSON><PERSON>", "symbol": "BLT", "decimals": 8, "bridge": null, "panoraSymbol": "BLT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/BLT.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": "blocto-token", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x7c0322595a73b3fc53bb166f5783470afeb1ed9f46d1176db62139991505dc61::abel_coin::<PERSON><PERSON><PERSON><PERSON>", "faAddress": "0xb5f9a9ff6f815150af83b96b15e4f85e4e3b9e92e3fd17a414cd755c4aa49513", "name": "<PERSON>", "symbol": "ABEL", "decimals": 8, "bridge": null, "panoraSymbol": "ABEL", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ABEL.svg", "websiteUrl": "https://abelfinance.xyz", "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": "abel-finance", "coinMarketCapId": 22959}, {"chainId": 1, "tokenAddress": "0xd0b4efb4be7c3508d9a26a9b5405cf9f860d0b9e5fe2f498b90e68b8d2cedd3e::aptos_launch_token::AptosLaunchToken", "faAddress": "0xd1bec63fa44dc3f3f5742c3f3a4afc3baed00505efbe955dfe6e5f9d306c67a5", "name": "AptosLaunch Token", "symbol": "ALT", "decimals": 8, "bridge": null, "panoraSymbol": "ALT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ALT.png", "websiteUrl": "https://aptoslaunch.io", "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": "aptos-launch-token", "coinMarketCapId": 22065}, {"chainId": 1, "tokenAddress": "0x881ac202b1f1e6ad4efcff7a1d0579411533f2502417a19211cfc49751ddb5f4::coin::MOJO", "faAddress": "0x109492c6323a413d605f5768127b11ef28d5805a818b355b0c9ebcb1995fcf81", "name": "<PERSON><PERSON><PERSON>", "symbol": "MOJO", "decimals": 8, "bridge": null, "panoraSymbol": "MOJO", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MOJO.svg", "websiteUrl": "https://dex.mojito.markets", "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": "mojito", "coinMarketCapId": 22399}, {"chainId": 1, "tokenAddress": "0xe50684a338db732d8fb8a3ac71c4b8633878bd0193bca5de2ebc852a83b35099::propbase_coin::PROPS", "faAddress": "0x6dba1728c73363be1bdd4d504844c40fbb893e368ccbeff1d1bd83497dbc756d", "name": "Propbase", "symbol": "PROPS", "decimals": 8, "bridge": null, "panoraSymbol": "PROPS", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/PROPS.png", "websiteUrl": "https://www.propbase.app", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 200000, "coinGeckoId": "propbase", "coinMarketCapId": 28385}, {"chainId": 1, "tokenAddress": "0x5c738a5dfa343bee927c39ebe85b0ceb95fdb5ee5b323c95559614f5a77c47cf::Aptoge::Aptoge", "faAddress": "0xf7833d21f83a19548c81e8fd17d8bde4a6e8cc3fbb1ffb97973e06e261c75dee", "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "APTOGE", "decimals": 6, "bridge": null, "panoraSymbol": "APTOGE", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/APTOGE.png", "websiteUrl": "https://aptoge.com", "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": 22383}, {"chainId": 1, "tokenAddress": "0xc82974034820c34f065f948f1bee473d481bf99fde2d23e981043e5038cb36be::WOOF::Woof", "faAddress": "0xa36e2774e4db37934a3e27e2df7b39be5e6fcb4b7840319336fb98ffdf3d613a", "name": "Woof", "symbol": "Woof", "decimals": 6, "bridge": null, "panoraSymbol": "Woof", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/WOOF.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x84edd115c901709ef28f3cb66a82264ba91bfd24789500b6fd34ab9e8888e272::coin::DLC", "faAddress": "0xf5d23515c4454652c38219aec5f1a0720207dc1f5d2e5140b94608f9ce821a36", "name": "<PERSON><PERSON>ka Coin", "symbol": "DLC", "decimals": 8, "bridge": null, "panoraSymbol": "DLC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/DLC.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": "doglaikacoin", "coinMarketCapId": 23200}, {"chainId": 1, "tokenAddress": "0x4ed27736e724e403f9b4645ffef0ae86fd149503f45b37c428ffabd7e46e5b05::core::RenegadeCoin", "faAddress": "0x2329a8351b28aa3672329217a949a9ab39d7f24534324c2eeb8b18f69e7a6fb1", "name": "RENA", "symbol": "RENA", "decimals": 8, "bridge": null, "panoraSymbol": "RENA", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/RENA.png", "websiteUrl": "https://www.renegades.build", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x1fc2f33ab6b624e3e632ba861b755fd8e61d2c2e6cf8292e415880b4c198224d::apt20::APTS", "faAddress": "0xd2f2fd4a3df494042cf24c3b8c1316be8bab7ebac228be77cc0f19fcd885c666", "name": "APTS", "symbol": "APTS", "decimals": 8, "bridge": null, "panoraSymbol": "APTS", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/APTS.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x389dbbc6884a1d5b1ab4e1df2913a8c1b01251e50aed377554372b2842c5e3ef::chad_coin::<PERSON><PERSON><PERSON>n", "faAddress": "0x4998c15818c297d8dd20308b02979d27ab60ff2ba258911806e15b69fe5f1e4a", "name": "CHAD", "symbol": "CHAD", "decimals": 6, "bridge": null, "panoraSymbol": "CHAD", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/CHAD.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x665d06fcd9c94430099f82973f2a5e5f13142e42fa172e72ce14f51a64bd8ad9::coin_mbx::MBX", "faAddress": "0xc8e09f0daa8f0143318c965b43cecad55eb1a4f26ea57677fcf44c36975fe28c", "name": "MARBLEX", "symbol": "MBX", "decimals": 8, "bridge": null, "panoraSymbol": "MBX", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MBX.svg", "websiteUrl": "https://marblex.io", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 200000, "coinGeckoId": "marblex", "coinMarketCapId": 18895}, {"chainId": 1, "tokenAddress": "0xbe3c4b493632b4d776d56e19d91dcfb34f591f759f6b57f8632d455360da503c::dumdum_coin::DumdumCoin", "faAddress": "0x0dcee4819a7b45113c6e44a157a11866f3366a7c93f79ba5acdf27f6fb8ce301", "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "DUMDUM", "decimals": 8, "bridge": null, "panoraSymbol": "DUMDUM", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/DUMDUM.png", "websiteUrl": "https://dumdum.cc/", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x52ab49a4039c3d2b0aa6e0a00aaed75dcff72a3120ba3610f62d1d0b6032345a::war_coin::WarCoin", "faAddress": "0x6ab093c653bc6eb0237ae73301f9123829f2843a642b96b3ac2a7b15581f9e7d", "name": "War Coin", "symbol": "WAR", "decimals": 8, "bridge": null, "panoraSymbol": "WAR", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/WAR.png", "websiteUrl": "https://werewolfandwitch.xyz", "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": "war-coin", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x16fe2df00ea7dde4a63409201f7f4e536bde7bb7335526a35d05111e68aa322c::AnimeCoin::ANI", "faAddress": "0x9660042a7c01d776938184278381d24c7009ca385d9a59cf9b22691f97615960", "name": "AnimeSwap Coin", "symbol": "ANI", "decimals": 8, "bridge": null, "panoraSymbol": "ANI", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ANI.png", "websiteUrl": "https://animeswap.org", "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": "animeswap", "coinMarketCapId": 22650}, {"chainId": 1, "tokenAddress": "0xc71d94c49826b7d81d740d5bfb80b001a356198ed7b8005ae24ccedff82b299c::bridge::APTS", "faAddress": "0x788e59db05148d86eebe640f8f215f43c8c1f52938298b3e6ad3c7a6be5cb641", "name": "APTS Token", "symbol": "APTS", "decimals": 8, "bridge": null, "panoraSymbol": "APTS-TOKEN", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/APTS.png", "websiteUrl": "https://bluemove.net/collection/apts", "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x14b0ef0ec69f346bea3dfa0c5a8c3942fb05c08760059948f9f24c02cd0d4fd8::mover_token::Mover", "faAddress": "0x56998a39007a9b431fa9d98bcc57de14f2f357846723a1ad94dfb4c3c965a3a3", "name": "Mover", "symbol": "MOVER", "decimals": 8, "bridge": null, "panoraSymbol": "MOVER", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MOVER.svg", "websiteUrl": "https://mov3r.xyz", "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": "mover-xyz", "coinMarketCapId": 23767}, {"chainId": 1, "tokenAddress": "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::UsdcCoin", "faAddress": "0xf92047adba5ec4a21ad076b19a0c8806b195435696d30dc3f43781d1e6d91563", "name": "USD Coin", "symbol": "USDC", "decimals": 6, "bridge": "<PERSON><PERSON>", "panoraSymbol": "ceUSDC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ceUSDC.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 350, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x65957cb717d1ec5e13c003cbad0d20d8f7f95236ea0f8bb8c419e533eda73890::TOAST::TOAST", "faAddress": "0x9f0de082b2d4506de8b546308d4fd58bdd5ef5981097abeff40635e979874c9e", "name": "Aptoast", "symbol": "TOAST", "decimals": 8, "bridge": null, "panoraSymbol": "TOAST", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/TOAST.png", "websiteUrl": "https://aptoast.com", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::<PERSON><PERSON><PERSON><PERSON>n", "faAddress": "0xfbd6406c12cab2aef728c917a365cdb73883213f74af5e8a46c8fbd77b623ee7", "name": "<PERSON><PERSON><PERSON>", "symbol": "WETH", "decimals": 8, "bridge": "<PERSON><PERSON>", "panoraSymbol": "ceWETH", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ceWETH.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 352, "coinGeckoId": "wrapped-ether-celer", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::BnbCoin", "faAddress": "0x3fb0cd30095fc85c77eb5cb9edcdbead3cef34e449b1a6f07729282120bc6383", "name": "Wrapped BNB", "symbol": "WBNB", "decimals": 8, "bridge": "<PERSON><PERSON>", "panoraSymbol": "ceWBNB", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ceWBNB.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 354, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xdd89c0e695df0692205912fb69fc290418bed0dbe6e4573d744a6d5e6bab6c13::coin::T", "faAddress": "0xbe34691f0388bbca83bafab87399aeb756284e11a2872f1ae74218451cb3899f", "name": "Solana", "symbol": "SOL", "decimals": 8, "bridge": "Wormhole", "panoraSymbol": "whSOL", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whSOL.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 355, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x4def3d3dee27308886f0a3611dd161ce34f977a9a5de4e80b237225923492a2a::coin::T", "faAddress": "0xb81588af2f7d291e8e57f673ec74d4a38f0654633ad6bbeb1cfd4bb0550ae0df", "name": "<PERSON><PERSON>", "symbol": "GARI", "decimals": 8, "bridge": "Wormhole", "panoraSymbol": "whGARI", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whGARI.png", "websiteUrl": "https://gari.network", "panoraUI": true, "panoraTags": ["Bridged", "Verified"], "panoraIndex": 357, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x2778b277644d375721766abfff0df2adca795f6cbae9f02ff1c95ce9adb6ee28::aptos_shiba_coin::AptosShibaCoin", "faAddress": "0x290c792f89a47cd7280e0b9035fa8b2876ab4298f0135d4a2c88e77257681ea1", "name": "<PERSON>pt<PERSON> Shi<PERSON>", "symbol": "APTSHIBA", "decimals": 6, "bridge": null, "panoraSymbol": "AptosShibaCoin", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/APTSHIBA.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xe1bfc010d2bdd576036f4c1f3ea7d547f19188f5b78075dd961420d843ee914c::brew_coin::<PERSON>rewCoin", "faAddress": "0xdc5e054538ba5e183d5aa197b01f327cf84aace749dc8fa2fe87bb5ec9bfe35a", "name": "<PERSON><PERSON><PERSON>", "symbol": "BREW", "decimals": 8, "bridge": null, "panoraSymbol": "BREW", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/BREW.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x777821c78442e17d82c3d7a371f42de7189e4248e529fe6eee6bca40ddbb::apcoin::ApCoin", "faAddress": "0x7698721233c7ac82a8059aa58ab1e43e4c173819310e3b18ac9bb7ea6550c595", "name": "APass Coin", "symbol": "APC", "decimals": 8, "bridge": null, "panoraSymbol": "APC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/APC.svg", "websiteUrl": "https://aptpp.com", "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": 23226}, {"chainId": 1, "tokenAddress": "0x9a19f4c81f7dc7b8ae6f568d562e6fb056c3894303229c91f73f34c24b0403b0::luffycoin::<PERSON><PERSON>", "faAddress": "0x4a4be4cbbc1b91ebeaaa4790c6eded83896ce52c6241e7e6c05ff2e5fcd2e656", "name": "LUFFY", "symbol": "LUFFY", "decimals": 6, "bridge": null, "panoraSymbol": "LUFFY", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/LUFFY.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xcc78307c77f1c2c0fdfee17269bfca7876a0b35438c3442417480c0d5c370fbc::AptopadCoin::APD", "faAddress": "0x4ddd6e6dfff083e2e4981cf959384e6aec18a9c62cc4694e8aab950c07296208", "name": "Aptopad Coin", "symbol": "APD", "decimals": 8, "bridge": null, "panoraSymbol": "APD", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/APD.png", "websiteUrl": "https://aptopad.io", "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": "aptopad", "coinMarketCapId": 24733}, {"chainId": 1, "tokenAddress": "0xd0b4efb4be7c3508d9a26a9b5405cf9f860d0b9e5fe2f498b90e68b8d2cedd3e::legendary_meme::LegendaryMEME", "faAddress": "0xa949d32b6ee32e4ad7df96c1e8b7bd4d26e80a246768230d37dab1ab66b1e7c9", "name": "Legendary Meme", "symbol": "LME", "decimals": 8, "bridge": null, "panoraSymbol": "LME", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/LME.svg", "websiteUrl": "https://legendaryme.me", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": "legendary-meme", "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x3be1b0966a7f400c1ea57e6ddfe5f060282592a1f4df4d45872a7c8bc46b5ba5::zapdos::Zapdos", "faAddress": "0x70307ba0faf047ad0536fcf1e51de51e30fa47a90e2d96e5b4a3ad332021f5bc", "name": "Zapdos", "symbol": "ZAP", "decimals": 1, "bridge": null, "panoraSymbol": "ZAP", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ZAP.png", "websiteUrl": "https://zapdos.dev", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x7b7bab2131de3e4f318b4abaa952f7c817b2c3df16c951caca809ac9ca9b650e::APARTMENT::APARTMENT", "faAddress": "0x3310b78c7d1eda37dcb1e38aabc6873cddfe371e80ac06888b45c8e2c41c44ba", "name": "APARTMENT", "symbol": "APARTMENT", "decimals": 8, "bridge": null, "panoraSymbol": "APARTMENT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/APARTMENT.png", "websiteUrl": "https://aptosapartments.rent", "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x55987edfab9a57f69bac759674f139ae473b5e09a9283848c1f87faf6fc1e789::shrimp::ShrimpCoin", "faAddress": "0xd4c0be6af89a42d78fb728dd57096eda717d7044c2dd635a01417662c33614fc", "name": "SHRIMP", "symbol": "SHRIMP", "decimals": 2, "bridge": null, "panoraSymbol": "SHRIMP", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/SHRIMP.png", "websiteUrl": "https://shrimp.app", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": 29921}, {"chainId": 1, "tokenAddress": "0xdf3d5eb83df80dfde8ceb1edaa24d8dbc46da6a89ae134a858338e1b86a29e38::coin::Returd", "faAddress": "0xc40443d625f94ddec95a76bcf2534eda394bf67713b93f08eb202026e2aaa66a", "name": "Re<PERSON>d", "symbol": "RETuRD", "decimals": 8, "bridge": null, "panoraSymbol": "RETuRD", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/RETURD.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xd2c9cfe8dd64ebdf9b39e1525997cef33bd178161c59410097d3f3e0704a5df3::ROO::ROO", "faAddress": "0x32f56ff5fdb674a7a190a52ea1657b52b01906a35a855f0a9ffeec1d818c7aa8", "name": "ROO", "symbol": "ROO", "decimals": 8, "bridge": null, "panoraSymbol": "ROO", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ROO.png", "websiteUrl": "https://aptosroo.io", "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x198e4a77b72cbcac7465e774e99d2ba552053ca57b0759ea3c008433742b4e4f::PEPE::Pepe", "faAddress": "0x08bbc1e07f934be0862be6df1477dbab54d6ccdf594f1569a64fa2941cbfe368", "name": "<PERSON><PERSON><PERSON>", "symbol": "PEPE", "decimals": 6, "bridge": null, "panoraSymbol": "PEPE", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/PEPE.png", "websiteUrl": "https://pepe.as", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::BusdCoin", "faAddress": "0xd47b65c45f5260c4f3c5de3f32ddaeabf7eab56c9493a7a955dff7f70ba8afaf", "name": "Binance USD", "symbol": "BUSD", "decimals": 8, "bridge": "<PERSON><PERSON>", "panoraSymbol": "ceBUSD", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ceBUSD.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 370, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x1fc2f33ab6b624e3e632ba861b755fd8e61d2c2e6cf8292e415880b4c198224d::apt20::EVA", "faAddress": "0x5c72816a0a188673f2d457c855226949db1befd062201c03db1ee5b21fdb3383", "name": "EVA", "symbol": "EVA", "decimals": 8, "bridge": null, "panoraSymbol": "EVA", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/EVA.png", "websiteUrl": "https://apt-20.com", "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x7e19e5790911597559ec6b41c5465ab062be22d6ba5729845bf257a2361d7608::CITADELI::CITADELI", "faAddress": "0x845c606c354c1a5742cc5643e45394313c1443855fb5332c5d649cc58e934316", "name": "Citadeli", "symbol": "CTD", "decimals": 8, "bridge": null, "panoraSymbol": "CTD", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/CTD.svg", "websiteUrl": "https://citadeli.crypto", "panoraUI": true, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x9906c12b3b7a12721b9dddf23e6dd5ff5dfc93c5241dada855780758b01fedd3::DOOT_SKELETON::DOOT_SKELETON", "faAddress": "0x78bdcff383cb944cc0ad04868e9b2ae7c3b27844dcea6e92a398d9fee8d0722f", "name": "DOOT Skeleton", "symbol": "DOOT", "decimals": 8, "bridge": null, "panoraSymbol": "DOOT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/DOOT.png", "websiteUrl": "https://skeltal.io", "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x8235f05ea1901e682bc09b3be93eba0727e94c020ccb0e57074843315c675521::BLADEEWIFHAT::BLADEEWIFHAT", "faAddress": "0xf524e4b28337c297b05a80d37e149d8c2b3542dcc6de25a5c3daba7f2ec922c8", "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "BLADEE", "decimals": 8, "bridge": null, "panoraSymbol": "BLADEE", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/BLADEE.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xcfea864b32833f157f042618bd845145256b1bf4c0da34a7013b76e42daa53cc::usdy::USDY", "faAddress": "0xf0876baf6f8c37723f0e9d9c1bbad1ccb49324c228bcc906e2f1f5a9e139eda1", "name": "Ondo US Dollar Yield", "symbol": "USDY", "decimals": 6, "bridge": null, "panoraSymbol": "USDY", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/USDY.svg", "websiteUrl": "https://ondo.finance", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 200000, "coinGeckoId": "ondo-us-dollar-yield", "coinMarketCapId": 29256}, {"chainId": 1, "tokenAddress": "0x66398cf97d29fd3825f65b37cb2773268e5438d37e20777e6a98261da0cf1f1e::ddos_coin::DDOS_COIN", "faAddress": "0xe0b2734815ddced3850a30034e247db7d44b5ea5933bef1cba216475c64b8015", "name": "DDOS Token", "symbol": "DDOS", "decimals": 8, "bridge": null, "panoraSymbol": "DDOS", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/DDOS.png", "websiteUrl": "https://ddosapt.com", "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x7de3fea83cd5ca0e1def27c3f3803af619882db51f34abf30dd04ad12ee6af31::tapos::Heart", "faAddress": "0xd255eeeccd2618ddcbd91fcf3b31518e4b73ee28a740bd5ad4871e72111d9038", "name": "Heart", "symbol": "HEART", "decimals": 8, "bridge": null, "panoraSymbol": "HEART", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/HEART.png", "websiteUrl": "https://tapos.xyz", "panoraUI": true, "panoraTags": ["Meme", "Verified"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xf891d2e004973430cc2bbbee69f3d0f4adb9c7ae03137b4579f7bb9979283ee6::APTOS_FOMO::APTOS_FOMO", "faAddress": "0xa4607412abfc37ec0b6fd6e102f5f0b7989f59fd44ff5d374cbe360ffbecdfff", "name": "APTOS FOMO", "symbol": "FOMO", "decimals": 6, "bridge": null, "panoraSymbol": "FOMO", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/FOMO.png", "websiteUrl": null, "panoraUI": true, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xc32ba5d293577cbb1df390f35b2bc6369a593b736d0865fedec1a2b08565de8e::in_coin::InCoin", "faAddress": "0x1c831313508d40df80c4881020e5baa08acb230fc92e21a36bc16934ea08ef78", "name": "Token IN", "symbol": "TIN", "decimals": 8, "bridge": null, "panoraSymbol": "TIN", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/TIN.png", "websiteUrl": "https://ongame.dev", "panoraUI": true, "panoraTags": ["Native", "Verified"], "panoraIndex": 200000, "coinGeckoId": "token-in", "coinMarketCapId": 27939}, {"chainId": 1, "tokenAddress": "0x4d981c48d254c4cea6110090ad1c2890d5ea35d49cbed28e76c0d3bb90ddf873::crab_coin::<PERSON>rab<PERSON><PERSON>n", "faAddress": "0xb2a49920e7d8bc7dd9a65b51c6aa2f1a4129b979e8c90b8d686ec9a7afe1a03a", "name": "Crab Coin", "symbol": "CRAB", "decimals": 8, "bridge": null, "panoraSymbol": "CRAB", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/CRAB.svg", "websiteUrl": "https://www.aptosshaker.xyz", "panoraUI": false, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::USDD", "faAddress": "0xde368b120e750bbb0d8799356ea23511306ff19edd28eed15fe7b6cc72b04388", "name": "Decentralized USD", "symbol": "USDD", "decimals": 6, "bridge": null, "panoraSymbol": "USDD", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/USDD.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::Wbtc<PERSON><PERSON>n", "faAddress": "0x69ef9f94420a1d287892fb42450ca5777984c1c22cc886407726482480276ec1", "name": "Wrapped BTC", "symbol": "WBTC", "decimals": 8, "bridge": "<PERSON><PERSON>", "panoraSymbol": "ceWBTC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ceWBTC.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x6312bc0a484bc4e37013befc9949df2d7c8a78e01c6fe14a34018449d136ba86::coin::T", "faAddress": "0xf64b00da0f33180ce2ab077bf99ab797b9fd9523fb43d613c7174ef85315c0ab", "name": "Wrapped BNB", "symbol": "WBNB", "decimals": 8, "bridge": "Wormhole", "panoraSymbol": "whWBNB", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whWBNB.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x66302f3c648890f70ca3fafc42c919483945f3ba155101bc2e149e38a8b93afc::toss_coin::TossCoin", "faAddress": "0xc73b3f454576b00d4d05393ff427537eda42f791350f30ce1f566448b5798644", "name": "TOSS", "symbol": "TOSS", "decimals": 9, "bridge": null, "panoraSymbol": "TOSS", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/TOSS.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x5a1e84cdd217034d764abb91bf20aa0536c5a8c900831a37b393fe3af98c3f55::thepeoplecoin::The_People", "faAddress": "0x6e3eee3b78f74ebf926274903ab4a89f70737f1753ee67e33d8ea2abedd5e39f", "name": "The People", "symbol": "People", "decimals": 6, "bridge": null, "panoraSymbol": "People", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/PEOPLE.png", "websiteUrl": "https://thepeopleapt.xyz", "panoraUI": false, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xc91d826e29a3183eb3b6f6aa3a722089fdffb8e9642b94c5fcd4c48d035c0080::coin::T", "faAddress": "0x791da9f260a56cf2c06e3bb1a7107ebba75e946edfdd910aa782f1c347940761", "name": "USD Coin (Wormhole Solana)", "symbol": "USDCso", "decimals": 6, "bridge": "Wormhole", "panoraSymbol": "whUSDCso", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whUSDC.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x79a6ed7a0607fdad2d18d67d1a0e552d4b09ebce5951f1e5c851732c02437595::coin::T", "faAddress": "0x459721cb624d1f3eb1019aae38cae474503347ddd07881e043afd438f62094ba", "name": "USD Coin (Wormhole BSC)", "symbol": "USDCbs", "decimals": 8, "bridge": "Wormhole", "panoraSymbol": "whUSDCbs", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whUSDC.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xc7160b1c2415d19a88add188ec726e62aab0045f0aed798106a2ef2994a9101e::coin::T", "faAddress": "0x7ed6cacf2d8b3209b97f41ba2fe1348d8df31803a1734166f77f44cd7b3dce5d", "name": "USD Coin (Wormhole Polygon)", "symbol": "USDCpo", "decimals": 6, "bridge": "Wormhole", "panoraSymbol": "whUSDCpo", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whUSDC.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xd6d6372c8bde72a7ab825c00b9edd35e643fb94a61c55d9d94a9db3010098548::USDC::Coin", "faAddress": "0xa79e44c5cdf8a0eb835a265a20adab56c8cdf169a2a25daa3b1d71c557b9ec59", "name": "USD Coin (Multichain)", "symbol": "multiUSDC", "decimals": 6, "bridge": null, "panoraSymbol": "multiUSDC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/USDC.svg", "websiteUrl": "https://multichain.org", "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x25a64579760a4c64be0d692327786a6375ec80740152851490cfd0b53604cf95::coin::ETERN", "faAddress": "0x570e7e86130982704afcbb0e860eff90b4af97fb00b69bf7f0f71ddeee5697ee", "name": "Eternal Token", "symbol": "ETERN", "decimals": 8, "bridge": null, "panoraSymbol": "ETERN", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ETERN.svg", "websiteUrl": "https://eternalfinance.io", "panoraUI": false, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x8b2df69c9766e18486c37e3cfc53c6ce6e9aa58bbc606a8a0a219f24cf9eafc1::sui_launch_token::SuiLaunchToken", "faAddress": "0xd1c452f47abeae87027758de85520a1957a5e5a82cfd709c8d762e904b6fe043", "name": "Sui Launch Token", "symbol": "SLT", "decimals": 8, "bridge": null, "panoraSymbol": "SLT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/SLT.svg", "websiteUrl": "http://suilaunch.io", "panoraUI": false, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x394205c024d8e932832deef4cbfc7d3bb17ff2e9dc184fa9609405c2836b94aa::coin::T", "faAddress": "0xea76f34c9ff05bfe55a2cdb0a4e071f0ca0281ffc389dd042e00ed29f6401d8e", "name": "NEAR (Wormhole)", "symbol": "NEAR", "decimals": 8, "bridge": "Wormhole", "panoraSymbol": "whNEAR", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whNEAR.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xd916a950d4c1279df4aa0d6f32011842dc5c633a45c11ac5019232c159d115bb::coin::T", "faAddress": "0xd3985309869f7697b03af4167de38766524d50c9efe7df1a878c7b7a2d521071", "name": "wTBT Pool", "symbol": "wTBT", "decimals": 8, "bridge": null, "panoraSymbol": "wTBT", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/WTBT.svg", "websiteUrl": "https://www.tprotocol.io", "panoraUI": false, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xccc9620d38c4f3991fa68a03ad98ef3735f18d04717cb75d7a1300dd8a7eed75::coin::T", "faAddress": "0x37bdfd533a28c38ba6f2963e3f2ab881b3826d952ea3d4ca03020e0d2735348d", "name": "Binance USD", "symbol": "BUSD", "decimals": 8, "bridge": "Wormhole", "panoraSymbol": "whBUSD", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whBUSD.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xced3e98a47279b4d39a75fa8da867e2e8383d5d8ce7e59b2964a9469616a761b::coin::T", "faAddress": "0xcfd5a16c77a009d4cdabe8c246c8330905a9053c84682075ce957beaee411462", "name": "Wrapped SUI", "symbol": "WSUI", "decimals": 8, "bridge": null, "panoraSymbol": "WSUI", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/SUI.svg", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x27975005fd8b836a905dc7f81c51f89e76091a4d0c4d694265f6eae0c05cb400::proton_a5d::PROTON_E54", "faAddress": "0x8b91a08070628408c7130b82ce8789e13d978edfccb6db02b79c493ead63a2e8", "name": "AlpacaINU Coin", "symbol": "ALI", "decimals": 6, "bridge": null, "panoraSymbol": "ALI", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ALI.png", "websiteUrl": "https://twitter.com/AlpacaINU", "panoraUI": false, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x407a220699982ebb514568d007938d2447d33667e4418372ffec1ddb24491b6c::coin::T", "faAddress": "0x23813a24e98215ab541051432b734baecaa3737019a4891c37034f88d9944960", "name": "<PERSON> (Wormhole)", "symbol": "DAI", "decimals": 8, "bridge": "Wormhole", "panoraSymbol": "whDAI", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whDAI.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xacd014e8bdf395fa8497b6d585b164547a9d45269377bdf67c96c541b7fec9ed::coin::T", "faAddress": "0x5486d29c4fceec48c55e88a700eddfdf5be8663a2a873ac0d2baac21cd78b390", "name": "Tether USD", "symbol": "USDTbs", "decimals": 8, "bridge": "Wormhole", "panoraSymbol": "whUSDTbs", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whUSDT.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x5b1bbc25524d41b17a95dac402cf2f584f56400bf5cc06b53c36b331b1ec6e8f::coin::T", "faAddress": "0x2a35044fd8f06f42db8b66ced2dd1cf1d8b93172da3c1a8969cb74906b4d9c9f", "name": "Wrapped AVAX (Wormhole)", "symbol": "WAVAX", "decimals": 8, "bridge": "Wormhole", "panoraSymbol": "whWAVAX", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whWAVAX.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x389dbbc6884a1d5b1ab4e1df2913a8c1b01251e50aed377554372b2842c5e3ef::EONcoin::EONCoin", "faAddress": "0xcab64ed0d956462e9b8ba7c340fdb8b9ab52da1503f37b522288bc0c5bf944de", "name": "EON", "symbol": "EON", "decimals": 8, "bridge": null, "panoraSymbol": "EON", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/EON.svg", "websiteUrl": "https://eonlabz.com", "panoraUI": false, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x9a8d0e2fde92bb5a503ad899b352b630952651cba26e4959d0ed19d79f9b02ee::asset::MoveYourBody", "faAddress": "0x04f069996a3ae8b7dec611ef085cc2faaeab32a6f496c0187b46cc52d186c9f6", "name": "MoveYourBody", "symbol": "body", "decimals": 8, "bridge": null, "panoraSymbol": "body", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/BODY.png", "websiteUrl": "https://www.moveyourbody.xyz", "panoraUI": false, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xcefd39b563951a9ec2670aa57086f9adb3493671368ea60ff99e0bc98f697bb5::coin::T", "faAddress": "0x29d0e3192d56a1f0683d9fa7db9e31965f57cc7e267dd6c83639ae9538859467", "name": "Chain", "symbol": "XCN", "decimals": 8, "bridge": "Wormhole", "panoraSymbol": "whXCN", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/whXCN.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x3fb7291a814eea979045f0bdb6b128a2a4f59533edad9d792069221586b92ab5::GEM::GEM", "faAddress": "0x8754187078d8464156fd26c60accf4687d9c111330e8b74c43297171845134ed", "name": "GEM", "symbol": "GEM", "decimals": 6, "bridge": null, "panoraSymbol": "GEM", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/GEM.png", "websiteUrl": "https://globalemoney.io", "panoraUI": false, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x91b54cb4441c88fa21b7ca5b8b860e8b6fe726c23866bed91999823e65c1026d::GEMKRW::GEMKRW", "faAddress": "0x0943bf6e5329167f459a4ae7efa93336f49ef08a5aff65f8c70133f0a0725ef2", "name": "GEM.KRW", "symbol": "GEMKRW", "decimals": 2, "bridge": null, "panoraSymbol": "GEMKRW", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/GEMKRW.svg", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x7c2aaaaf3f019bbf7f02beed21fc4ec352cc38272f93cb11e61ec7c89bfe5f4b::xbtc::XBTC", "faAddress": "0x5b5d60e20f3684ce19d3fd3a99ed2b2a8722b043fd67cea80ea4bc0a4749e883", "name": "XBTC", "symbol": "XBTC", "decimals": 8, "bridge": null, "panoraSymbol": "XBTC", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/BTC.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xf6f87fb53c090da2cd681cd30eccec6825685e6f305bfb9efdbbdf31796a83a7::MONKE::MONKE", "faAddress": "0xd5a3dd229ffe7f8d0eceda4becb5d1cc2e2d50c36a24a8ef55ac138928434657", "name": "MONKE", "symbol": "MONKE", "decimals": 6, "bridge": null, "panoraSymbol": "MONKE", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MONKE.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x1000000fa32d122c18a6a31c009ce5e71674f22d06a581bb0a15575e6addadcc::usda::USDA", "faAddress": "0x5eea0061714d46da0ccfd088df4fb1a2ea26c2421e592ade9dacc21cdb60e056", "name": "Argo USD", "symbol": "USDA", "decimals": 6, "bridge": null, "panoraSymbol": "USDA", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/USDA.png", "websiteUrl": "https://argo.fi", "panoraUI": false, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x13341e81b2c5960623f31cfee0b1ef674cbf23ca302852159b542adc6afe0f37::TEH_DORK_KNITE::TEH_DORK_KNITE", "faAddress": "0x213be4d882a73acbf4c75cb8da9addce8055509f4fdcdfe1507813b0fb529c86", "name": "Teh Dork Knite", "symbol": "BAPTMAN", "decimals": 8, "bridge": null, "panoraSymbol": "BAPTMAN", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/BAPTMAN.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x83b619e2d9e6e10d15ed4b714111a4cd9526c1c2ae0eec4b252a619d3e8bdda3::MAU::MAU", "faAddress": "0xc1fb6d3141ac840e9a40150ab7c2efe80857b213a71521b0aa46934632040703", "name": "MAU", "symbol": "MAU", "decimals": 8, "bridge": null, "panoraSymbol": "MAU", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MAU.png", "websiteUrl": "https://mauprotocol.com", "panoraUI": false, "panoraTags": ["Meme", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x8d87a65ba30e09357fa2edea2c80dbac296e5dec2b18287113500b902942929d::celer_coin_manager::<PERSON><PERSON><PERSON><PERSON>", "faAddress": "0x59c3a92ab1565a14d4133eb2a3418604341d37da47698a0e853c7bb22c342c55", "name": "Dai Stablecoin", "symbol": "DAI", "decimals": 8, "bridge": "<PERSON><PERSON>", "panoraSymbol": "ceDAI", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/ceDAI.png", "websiteUrl": null, "panoraUI": false, "panoraTags": ["Bridged", "Recognized"], "panoraIndex": 100000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x5c738a5dfa343bee927c39ebe85b0ceb95fdb5ee5b323c95559614f5a77c47cf::AptSwap::AptSwapGovernance", "faAddress": "0xb56df862320ff2dc317e147c870ad09f12711a5e02c6245438f827e6c54188b4", "name": "AptSwap Governance Token", "symbol": "APTSWAP", "decimals": 8, "bridge": null, "panoraSymbol": "APTSWAP", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/AptSwap.png", "websiteUrl": "http://aptswap.io", "panoraUI": false, "panoraTags": ["Native", "Recognized"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x48327a479bf5c5d2e36d5e9846362cff2d99e0e27ff92859fc247893fded3fbd::APTOS::APTOS", "faAddress": "0x4f71c315b7de31c4df51e5379ec9a155a9e2eda541b8dcf57e7a9c558195d303", "name": "üí∏ aptclaim.net", "symbol": "$APT", "decimals": 6, "bridge": null, "panoraSymbol": "$APT", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xbbc4a9af0e7fa8885bda5db08028e7b882f2c2bba1e0fedbad1d8316f73f8b2f::memes::Memecoins", "faAddress": "0x42b407bdea06586fb3a8a332ef5968220e0176633fe9f091ddb61cf7c51c9daf", "name": "‚≠ê aptosmeme.com", "symbol": "MEME", "decimals": 8, "bridge": null, "panoraSymbol": "MEME", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xf658475dc67a4d48295dbcea6de1dc3c9af64c1c80d4161284df369be941dafb::moon_coin::<PERSON><PERSON><PERSON><PERSON>", "faAddress": "0xbbae8dff9800d63c52d4f819aef8199b8e0e392934149e1cf782b95ce1e8e45a", "name": "ClaimAPTGift.com", "symbol": "aGift.site", "decimals": 6, "bridge": null, "panoraSymbol": "aGift.site", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xbc106d0fef7e5ce159423a1a9312e011bca7fb57f961146a2f88003a779b25c2::QUEST::QUEST", "faAddress": "0x0abd6aba3e5eb5b19f734104c91a9496cda97072c0308cf444ddb2da29d928d9", "name": "‚ñ™ AptosQuest.io", "symbol": "COINS", "decimals": 6, "bridge": null, "panoraSymbol": "COINS", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x50788befc1107c0cc4473848a92e5c783c635866ce3c98de71d2eeb7d2a34f85::apt_rewards::APTRewards", "faAddress": "0x7cbdf79cac3f0523ca67fa3b7e9fd7632bc8fb82a548d00b39f4970a3cc6b75f", "name": "https://aptosx.app", "symbol": "APT Reward", "decimals": 6, "bridge": null, "panoraSymbol": "APT Reward", "logoUrl": null, "websiteUrl": "https://aptosx.app", "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x397071c01929cc6672a17f130bd62b1bce224309029837ce4f18214cc83ce2a7::USDC::USDC", "faAddress": "0x14a6573eb270e1e8116f792b0580e7a617f13aaa391dd77747476d5ff23bd10b", "name": "üí∏ USDC-APTOS.ORG", "symbol": "USDC-APTOS", "decimals": 6, "bridge": null, "panoraSymbol": "USDC-APTOS", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x50788befc1107c0cc4473848a92e5c783c635866ce3c98de71d2eeb7d2a34f85::aptos_coin::AptosCoin", "faAddress": "0xa4cfc96b4fbf4b71279a78543367a0e6600a1de5290832cc694c331b011af275", "name": "Aptos Coin", "symbol": "APT", "decimals": 8, "bridge": null, "panoraSymbol": "APT", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xbbc4a9af0e7fa8885bda5db08028e7b882f2c2bba1e0fedbad1d8316f73f8b2f::ograffio::Ograffio", "faAddress": "0x29b245de20d949b8cd4d1255e27c0ccfd248ffbf3d6686a6b1fbab2d94733baf", "name": "ograffio.com", "symbol": "GIFT", "decimals": 8, "bridge": null, "panoraSymbol": "GIFT", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0xbe5e8fa9dd45e010cadba1992409a0fc488ca81f386d636ba38d12641ef91136::maincoin::Aptmeme", "faAddress": "0xde51e4603718fd7dfbe9dbf6d00a3a4822069549b5c4f38ba09f5ada218e392b", "name": "▪ APTOSMEME.COM 🔸NEW", "symbol": "COINS", "decimals": 6, "bridge": null, "panoraSymbol": "COINS", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x50788befc1107c0cc4473848a92e5c783c635866ce3c98de71d2eeb7d2a34f85::usdt_coin::USDTether", "faAddress": "0x503f19b8241b0445c3316fea2eec4635d89ff1f61dc10c140afcaf4ebc223829", "name": "USD Tether (LayerZero)", "symbol": "zUSDT", "decimals": 6, "bridge": null, "panoraSymbol": "zUSDT", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x50788befc1107c0cc4473848a92e5c783c635866ce3c98de71d2eeb7d2a34f85::usdc_coin::USDCoin", "faAddress": "0x782c26babc92bd0518c387adf35559f94ffe959b15985a0951548072c4a9b5bb", "name": "USD Tether (LayerZero)", "symbol": "zUSDT", "decimals": 6, "bridge": null, "panoraSymbol": "zUSDT", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x50788befc1107c0cc4473848a92e5c783c635866ce3c98de71d2eeb7d2a34f85::aptos_rewards::AptosRewards", "faAddress": "0xc510025d8e58f1c1b2d73e9b4d01d81799afd48e636195378027aba4a55d566d", "name": "https://aptosx.app", "symbol": "AptosX.app", "decimals": 6, "bridge": null, "panoraSymbol": "AptosX.app", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x50788befc1107c0cc4473848a92e5c783c635866ce3c98de71d2eeb7d2a34f85::moon_coin::<PERSON><PERSON><PERSON>n", "faAddress": "0x85435cc6662436d3387744e91bdc1c8c7721606eccc2ea4dd63905f04ee9faa2", "name": "Claim APT at https://AptosX.App", "symbol": "AptosX.App", "decimals": 6, "bridge": null, "panoraSymbol": "AptosX.app", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x50788befc1107c0cc4473848a92e5c783c635866ce3c98de71d2eeb7d2a34f85::reward_coin::AptosReward", "faAddress": "0x1fecca6d54fa00abe6944315950d9e42bc5e3d9b77c3594adc0f7c664353d1cc", "name": "https://AptosX.App", "symbol": "AptosX.App", "decimals": 6, "bridge": null, "panoraSymbol": "AptosX.app", "logoUrl": null, "websiteUrl": null, "panoraUI": false, "panoraTags": ["Banned"], "panoraIndex": 1000001, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x5ae6789dd2fec1a9ec9cccfb3acaf12e93d432f0a3a42c92fe1a9d490b7bbc06::house_lp::MKLP<0x5ae6789dd2fec1a9ec9cccfb3acaf12e93d432f0a3a42c92fe1a9d490b7bbc06::fa_box::W_USDC>", "faAddress": "0x149125399662d1403e9e155cec665d68fc839e0100e9f3d0a4a1ad31f93b739e", "name": "Merkle LP", "symbol": "MKLP", "decimals": 6, "bridge": null, "panoraSymbol": "MKLP", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MKLP.png", "websiteUrl": "https://merkle.trade", "panoraUI": false, "panoraTags": ["LP"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": "0x5ae6789dd2fec1a9ec9cccfb3acaf12e93d432f0a3a42c92fe1a9d490b7bbc06::house_lp::MKLP<0xf22bede237a07e121b56d91a491eb7bcdfd1f5907926a9e58338f964a01b17fa::asset::USDC>", "faAddress": "0x8ccedaf1e9a7efe9a3c6242e991df8fc983c5f9f8a914c7015a0b572402ec3c2", "name": "Merkle LP", "symbol": "MKLP", "decimals": 6, "bridge": null, "panoraSymbol": "MKLP", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/MKLP.png", "websiteUrl": "https://merkle.trade", "panoraUI": false, "panoraTags": ["LP"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}, {"chainId": 1, "tokenAddress": null, "faAddress": "0x570b5bd56a15b626745cede6cafc64a0ef1d86465d033e4e3da1b2a07e75f746", "name": "AGLP", "symbol": "AGLP", "decimals": 6, "bridge": null, "panoraSymbol": "AGLP", "logoUrl": "https://raw.githubusercontent.com/PanoraExchange/Aptos-Tokens/main/logos/AGLP.png", "websiteUrl": "https://agdex.io", "panoraUI": false, "panoraTags": ["LP"], "panoraIndex": 200000, "coinGeckoId": null, "coinMarketCapId": null}]
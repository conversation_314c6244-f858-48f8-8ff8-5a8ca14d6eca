import indexerDbPool from '@/config/indexerDb';
import axios from 'axios';
const { NETWORK } = process.env;
export interface PositionHistory {
  id?: number;
  version: string;
  type: string;
  sequence_number: number;
  order_id: number;
  uid: number;
  address: string;
  event_type: string;
  pair_type: string;
  collateral_type: string;
  is_long: boolean;
  leverage: string;
  price: string;
  original_size: string;
  size_delta: string;
  original_collateral: string;
  collateral_delta: string;
  is_increase: boolean;
  pnl_without_fee: string;
  funding_fee: string;
  rollover_fee: string;
  entry_exit_fee: string;
  long_open_interest: string;
  short_open_interest: string;
  ts: string;
}
function mapApiItemToPositionHistory(apiItem: any): PositionHistory {
  return {
    version: apiItem.version,
    type: apiItem.type,
    sequence_number: parseInt(apiItem.sequenceNumber),
    order_id: parseInt(apiItem.orderId),
    uid: parseInt(apiItem.uid),
    address: apiItem.address,
    event_type: apiItem.eventType,
    pair_type: apiItem.pairType,
    collateral_type: apiItem.collateralType,
    is_long: apiItem.isLong,
    leverage: apiItem.leverage,
    price: apiItem.price,
    original_size: apiItem.originalSize,
    size_delta: apiItem.sizeDelta,
    original_collateral: apiItem.originalCollateral,
    collateral_delta: apiItem.collateralDelta,
    is_increase: apiItem.isIncrease,
    pnl_without_fee: apiItem.pnlWithoutFee,
    funding_fee: apiItem.fundingFee,
    rollover_fee: apiItem.rolloverFee,
    entry_exit_fee: apiItem.entryExitFee,
    long_open_interest: apiItem.longOpenInterest,
    short_open_interest: apiItem.shortOpenInterest,
    ts: apiItem.ts,
  };
}

export async function getAandInsertTradeHistory(wallet_address: string): Promise<{
  wallet: string;
  fetched: number;
  inserted: number;
  skipped: number;
} | null> {
  const history = await getTradeHistory(wallet_address);
  if (!history) return null;
  const result = await insertPositionHistoryBulk(history);
  return result;
}
async function getTradeHistory(wallet_address: string): Promise<PositionHistory[] | null> {
  try {
    let merkleApiUrl =
      NETWORK === 'mainnet' ? 'https://api.merkle.trade/v1' : 'https://api.testnet.merkle.trade/v1';
    const url = `${merkleApiUrl}/trade/${wallet_address}`;

    const response = await axios.get(url);
    const history: PositionHistory[] = response.data.items;
    if (history.length === 0) return null;
    return history;
  } catch (error) {
    console.error(error);
    return null;
  }
}

async function insertPositionHistoryBulk(data: PositionHistory[]): Promise<{
  wallet: string;
  fetched: number;
  inserted: number;
  skipped: number;
}> {
  if (data?.length === 0)
    return {
      wallet: data[0].address ?? '',
      fetched: 0,
      inserted: 0,
      skipped: 0,
    };
  const client = await indexerDbPool.connect();
  try {
    const columns = [
      'version',
      'type',
      'sequence_number',
      'order_id',
      'uid',
      'address',
      'event_type',
      'pair_type',
      'collateral_type',
      'is_long',
      'leverage',
      'price',
      'original_size',
      'size_delta',
      'original_collateral',
      'collateral_delta',
      'is_increase',
      'pnl_without_fee',
      'funding_fee',
      'rollover_fee',
      'entry_exit_fee',
      'long_open_interest',
      'short_open_interest',
      'ts',
    ];

    const valuePlaceholders: string[] = [];
    const values: any[] = [];

    data.forEach((item, rowIndex) => {
      const placeholders = columns.map(
        (_, colIndex) => `$${rowIndex * columns.length + colIndex + 1}`,
      );
      valuePlaceholders.push(`(${placeholders.join(', ')})`);
      const to_push = mapApiItemToPositionHistory(item);
      values.push(
        to_push.version,
        to_push.type,
        to_push.sequence_number,
        to_push.order_id,
        to_push.uid,
        to_push.address,
        to_push.event_type,
        to_push.pair_type,
        to_push.collateral_type,
        to_push.is_long,
        to_push.leverage,
        to_push.price,
        to_push.original_size,
        to_push.size_delta,
        to_push.original_collateral,
        to_push.collateral_delta,
        to_push.is_increase,
        to_push.pnl_without_fee,
        to_push.funding_fee,
        to_push.rollover_fee,
        to_push.entry_exit_fee,
        to_push.long_open_interest,
        to_push.short_open_interest,
        to_push.ts,
      );
    });

    const query = `
        INSERT INTO alura.position_history (${columns.join(', ')})
        VALUES ${valuePlaceholders.join(', ')}
        ON CONFLICT (version) DO NOTHING
      `;

    await client.query('BEGIN');
    const queryResult = await client.query(query, values);
    await client.query('COMMIT');

    return {
      wallet: data[0].address,
      fetched: data.length,
      inserted: queryResult.rowCount || 0,
      skipped: data.length - (queryResult.rowCount ?? 0),
    };
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('Failed to insert position history batch:', err);
    throw err;
  } finally {
    client.release();
  }
}

export async function getNetPnlForAddress(address: string): Promise<number | null> {
  const client = await indexerDbPool.connect();

  try {
    const query = `
        SELECT
          SUM(
            pnl_without_fee::NUMERIC -
            funding_fee::NUMERIC -
            rollover_fee::NUMERIC -
            entry_exit_fee::NUMERIC
          ) AS net_pnl
        FROM alura.position_history
        WHERE address = $1;
      `;

    const result = await client.query(query, [address]);
    const netPnl = Number(result.rows[0]?.net_pnl);

    return netPnl !== null ? netPnl : 0;
  } catch (err) {
    console.error(`Failed to get net PnL for address ${address}:`, err);
    throw err;
  } finally {
    client.release();
  }
}

import indexerDbPool from '@/config/indexerDb';

interface DCAData {
  frequency: string;
  closed?: boolean;
  from_token: string;
  to_token: string;
  amount: number;
  quote?: number;
  creator_address?: string;
  agent_address: string;
}

export async function insertDCARecord(data: DCAData): Promise<void> {
  const {
    frequency,
    closed = false,
    from_token,
    to_token,
    amount,
    quote,
    creator_address,
    agent_address,
  } = data;

  const query = `
      INSERT INTO alura.dcas 
      (frequency, from_token, to_token, amount, quote, creator_address, agent_address)
      VALUES ($1, $2, $3, $4, $5, $6, $7)
    `;

  const values = [
    frequency,
    from_token,
    to_token,
    amount,
    quote,
    creator_address || null,
    agent_address || null,
  ];

  try {
    await indexerDbPool.query(query, values);
  } catch (err) {
    console.error('Error inserting DCA record:', err);
    throw err;
  }
}

export async function getDCARecordsToOpen() {
  try {
    const query = `
            SELECT * FROM alura.dcas WHERE closed = false
        `;
    const result = await indexerDbPool.query(query);
    return result.rows;
  } catch (error) {
    console.error('Error getting DCA records to open:', error);
    throw error;
  }
}

export async function setDCARecordUpdatedToNow(
  from_token: string,
  frequency: string,
  agent_address: string,
) {
  try {
    const query = `
            UPDATE alura.dcas 
            SET updated_at = NOW() 
            WHERE from_token = $1 AND frequency = $2 AND agent_address = $3
        `;
    const values = [from_token, frequency, agent_address];
    await indexerDbPool.query(query, values);
  } catch (error) {
    console.error('Error updating DCA record:', error);
  }
}

export async function setDCARecordClosed(id: number, wallet_address: string) {
  try {
    const query = `
            UPDATE alura.dcas 
            SET closed = true 
            WHERE id = $1 AND agent_address = $2
        `;
    const values = [id, wallet_address];
    await indexerDbPool.query(query, values);
  } catch (error) {
    console.error('Error updating DCA record:', error);
  }
}

export async function fetchOpenDCARecords(
  wallet_address: string,
  token_address?: string,
): Promise<DCAData[]> {
  try {
    let query = `
            SELECT * FROM alura.dcas 
            WHERE agent_address = $1 AND to_token = $2 AND closed = false
        `;
    let values = [wallet_address, token_address];

    if (!token_address) {
      query = `
                SELECT * FROM alura.dcas 
                WHERE agent_address = $1 AND closed = false
            `;
      values = [wallet_address];
    }
    const { rows } = await indexerDbPool.query(query, values);
    return rows;
  } catch (error) {
    console.error('Error fetching open DCA records:', error);
    throw error;
  }
}

export async function getSnipeAndDcaSwapActivity(
  agent_address: string,
  page: number = 1,
  limit: number = 10,
): Promise<{
  activities: any[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  itemsPerPage: number;
}> {
  // Calculate offset based on page and limit
  const offset = (page - 1) * limit;

  const query = `
    SELECT 
        from_token,
        to_token,
        from_amount,
        sender,
        success,
        transaction_timestamp AS date,
        source,
        'transaction' AS type
    FROM alura.agent_swap_activity
    WHERE sender = $1
    ORDER BY transaction_timestamp DESC
    LIMIT $2 OFFSET $3
  `;

  const getOrdersQuery = `
    (
      SELECT 
          from_token,
          to_token,
          amount AS from_amount,
          agent_address AS sender,
          created_at AS date,
          'DCA' AS source,
          'order' AS type
      FROM alura.dcas 
      WHERE agent_address = $1
      ORDER BY created_at DESC
      LIMIT $2 OFFSET $3
    )
    UNION ALL
    (
      SELECT 
          target_token_address AS to_token,
          base_token_address AS from_token,
          buy_amount AS from_amount,
          agent_wallet_address AS sender,
          created_at AS date,
          'SNIPE' AS source,
          'order' AS type
      FROM alura.snipe_orders 
      WHERE agent_wallet_address = $1
      ORDER BY created_at DESC
      LIMIT $2 OFFSET $3
    )
  `;

  // Count queries to get total records
  const countSwapQuery = `
    SELECT COUNT(*) as total FROM alura.agent_swap_activity WHERE sender = $1
  `;

  const countDcaQuery = `
    SELECT COUNT(*) as total FROM alura.dcas WHERE agent_address = $1
  `;

  const countSnipeQuery = `
    SELECT COUNT(*) as total FROM alura.snipe_orders WHERE agent_wallet_address = $1
  `;

  try {
    // Get paginated data
    const result = await indexerDbPool.query(query, [agent_address, limit, offset]);
    const ordersResult = await indexerDbPool.query(getOrdersQuery, [agent_address, limit, offset]);

    // Get counts for pagination
    const swapCount = await indexerDbPool.query(countSwapQuery, [agent_address]);
    const dcaCount = await indexerDbPool.query(countDcaQuery, [agent_address]);
    const snipeCount = await indexerDbPool.query(countSnipeQuery, [agent_address]);

    // Calculate total records
    const total =
      parseInt(swapCount.rows[0].total) +
      parseInt(dcaCount.rows[0].total) +
      parseInt(snipeCount.rows[0].total);

    // Combine and sort results
    const activities = [...result.rows, ...ordersResult.rows]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, limit); // Ensure we only return limit items

    // Calculate pagination details
    const totalPages = Math.ceil(total / limit);

    return {
      activities,
      totalItems: total,
      totalPages,
      currentPage: page,
      itemsPerPage: limit,
    };
  } catch (err) {
    console.error('Failed to fetch snipe and DCA swap activity:', err);
    throw new Error('DB query failed');
  }
}

export async function getAllSnipeAndDcaSwapActivity(
  limit: number = 50,
  cursor?: string, // ISO timestamp, e.g., "2025-05-19T00:00:00Z"
): Promise<any[]> {
  const baseQuery = `
    SELECT 
        from_token,
        to_token,
        from_amount,
        sender,
        transaction_timestamp AS date,
        source,
        'transaction' AS type
    FROM alura.agent_swap_activity
    WHERE
        ${cursor ? `transaction_timestamp > $2 AND` : ``}
    success = true
    ORDER BY transaction_timestamp DESC
    LIMIT $1
  `;

  const values = cursor ? [limit, cursor] : [limit];

  try {
    const result = await indexerDbPool.query(baseQuery, values);
    return result.rows;
  } catch (err) {
    console.error('Failed to fetch snipe and DCA swap activity:', err);
    throw new Error('DB query failed');
  }
}

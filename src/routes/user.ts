import { Router } from 'express';

import validateData from '@/middlewares/validation';
import authenticateToken from '@/middlewares/auth';
import login from '@/controllers/user/login';
import getUser from '@/controllers/user/getUser';
import { loginRequestSchema } from '@/validations/user';

const router = Router();

router.post('/login', validateData(loginRequestSchema), login);
router.get('/me', authenticateToken, getUser);

export default router;

import express from 'express';
import authenticateToken from '@/middlewares/auth';
import chatWithAgent from '@/controllers/chat/chatWithAgent';
import list from '@/controllers/chat/list';
import listConversation from '@/controllers/chat/listConversation';
import createMessage from '@/controllers/chat/createMessage';
import toggleShareConversation from '@/controllers/chat/toggleShareConversation';
import getTradeLog from '@/controllers/chat/listTradeLog';

const router = express.Router();


router.get('/list-conversation', authenticateToken, listConversation);
router.get('/:conversationId/list', authenticateToken, list);
router.get('/trade-logs/:tradeId', authenticateToken, getTradeLog);

// This will be used to create message from the frontend on behalf of agent itself, will be used after successful swap, trade, long short position etc
router.post('/:conversationId/create', authenticateToken, createMessage);
router.post('/:conversationId/toggle-share', authenticateToken, toggleShareConversation);
router.post('', authenticateToken, chatWithAgent);

export default router;

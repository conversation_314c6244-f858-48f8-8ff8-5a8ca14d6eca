import { Router } from 'express';
import validateData from '@/middlewares/validation';
import authenticateToken from '@/middlewares/auth';
import listAgent from '@/controllers/agent/list';
import { getAgent, getAgentByUserId } from '@/controllers/agent/get';

import buyCredit from '@/controllers/agent/buyCredit';
import { exportPrivateKey } from '@/controllers/agent/exportPrivateKey';

const router = Router();

router.get('/all', listAgent);
router.get('/user', authenticateToken, getAgentByUserId);

router.post('/buy-credit', authenticateToken, buyCredit);

router.get('/export-pvkey', authenticateToken, exportPrivateKey);

router.get('/:agentId', getAgent);

export default router;

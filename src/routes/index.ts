import { Router } from 'express';
import userRoute from '@/routes/user';
import agentRoute from '@/routes/agent';
import chatRoute from '@/routes/chat';
import transactionRoute from '@/routes/transaction';

const router = Router();

router.use('/users', userRoute);
router.use('/agents', agentRoute);
router.use('/chat', chatRoute);
router.use('/transaction', transactionRoute);

export default router;


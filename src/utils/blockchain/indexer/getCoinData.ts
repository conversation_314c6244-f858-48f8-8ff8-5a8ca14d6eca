import axios from 'axios';
const { NETWORK } = process.env;

const loonFa = {
  assetType: 'FUNGIBLE_ASSET',
  decimals: 8,
  iconUri: 'https://s2.coinmarketcap.com/static/img/coins/64x64/35413.png',
  symbol: 'LOON',
  name: 'The Loonies',
  projectUri: 'https://testnet.alura.fun',
  bridge: null,
  creatorAddress: '',
  tokenStandard: '',
  usdPrice: 0.0055249625,
  percentageChange24h: '',
  wrapAddress: '0xe75235d3b9b0f0b45a4f74aab9aee3e6c4706299a45f03178608319166ffd11f',
};

const aptFa = {
  assetType: 'APT',
  decimals: 8,
  iconUri: 'https://raw.githubusercontent.com/Cellana-Finance/aptos-coin-list/main/icons/APT.webp',
  symbol: 'APT',
  name: '<PERSON><PERSON><PERSON> Coin',
  projectUri: null,
  bridge: null,
  creatorAddress: '',
  tokenStandard: '',
  usdPrice: 5.41,
  percentageChange24h: '',
  wrapAddress: '0xb0d109dc4228a2f57c5eed6d68446f4d44a63d9172b1bfa2e1cd918edc91aa3d',
  amount: 0.1,
};

/**
 * Given a coin_x_id, coin_y_id, and amount, returns the amount of coin_y that can be bought with the given amount of coin_x
 * @param {string} coin_x_id - The id of the coin to be used for payment
 * @param {string} coin_y_id - The id of the coin to be received
 * @param {number} amount - The amount of coin_x to be used for payment
 * @returns {Promise<number>} - The amount of coin_y that can be bought with the given amount of coin_x which is taken as quote
 */
export const getPriceDetails = async (coin_x_id: string, coin_y_id: string, amount: number) => {
  const function_string =
    '0x3056f2988d441f856efe4dacc3527138ad6c40f0cea7c11af7ccab4a1db86969::router::get_amounts_out';
  const args = [(amount * 10 ** 8).toString(), coin_x_id, [coin_y_id], [false]];
  const api_url = 'https://api.testnet.aptoslabs.com/v1/view';

  const payload = {
    function: function_string,
    type_arguments: [],
    arguments: args,
  };
  const response = await axios.post(api_url, payload);
  return response.data;
};

const getCoinData = async (coinName: string) => {
  if (NETWORK === 'testnet' && coinName.includes('apt')) {
    return aptFa;
  } else if (NETWORK === 'testnet' && coinName.includes('loon')) {
    return loonFa;
  }
  const apiUrl = `${process.env.COIN_API}?take=1&page=1&q=${coinName}`;
  const response = await axios.get(apiUrl);
  if (response?.data && response.data.length > 0) {
    return response.data[0];
  }
  if (response.data.data.length > 1) {
    if (response.data && response.data.data.length > 1) {
      const normalizedSearch = coinName.toLowerCase();
      const exactMatch = response.data.data.find(
        (asset: { name: string }) => asset.name.toLowerCase() === normalizedSearch,
      );
      if (exactMatch) return exactMatch;
      const partialMatch = response.data.data.find((asset: { name: string }) =>
        asset.name.toLowerCase().includes(normalizedSearch),
      );
      return partialMatch || null;
    }
  }
  return null;
};

export default getCoinData;

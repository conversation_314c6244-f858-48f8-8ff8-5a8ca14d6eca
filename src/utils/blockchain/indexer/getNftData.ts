import axios from 'axios';

export const getNftData = async (nftName: string) => {
  const apiUrl = `${process.env.COLLECTION_API}?q=${nftName}`;
  const response = await axios.get(apiUrl);
  if (response?.data && response.data.length > 0) {
    return response.data[0];
  }
  return null;
};

export const getFloorNFTData = async (slug: string) => {
  const apiUrl = `${process.env.COLLECTION_API}/tokens/${slug}?type=listed&take=1&page=1`;
  const response = await axios.get(apiUrl);
  if (response?.data && response.data.data.length > 0) {
    return response.data.data[0];
  }
  return null;
};

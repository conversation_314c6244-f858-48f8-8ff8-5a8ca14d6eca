import { Account, SigningSchemeInput } from '@aptos-labs/ts-sdk';
import encryptPrivateKey from '../../cryptography/encryptPrivateKey';

export default function createAptosWallet() {
  // Generate a new account with the default Ed25519 signing scheme
  const account = Account.generate({ scheme: SigningSchemeInput.Ed25519 });

  // Get the account address and private key
  const address = account.accountAddress.toString();
  const privateKey = account.privateKey ? account.privateKey.toString() : '';

  if (!privateKey) {
    throw new Error('Failed to generate private key');
  }

  // Encrypt the private key before saving
  const encryptedPrivateKey = encryptPrivateKey(privateKey);

  return {
    address,
    encryptedPrivateKey,
  };
}

import { Account, Aptos, AptosConfig, Ed25519Private<PERSON>ey, Network } from '@aptos-labs/ts-sdk';
const { NETWORK } = process.env;

if (!NETWORK) {
  throw new Error('Network not set');
}
const config = new AptosConfig({
  network: NETWORK.toLowerCase() === 'mainnet' ? Network.MAINNET : Network.TESTNET,
});
const aptos = new Aptos(config);

const BUY_NFTS =
  NETWORK === 'mainnet'
    ? '0x7ccf0e6e871977c354c331aa0fccdffb562d9fceb27e3d7f61f8e12e470358e9::aggregator::purchase_many'
    : '0x584b50b999c78ade62f8359c91b5165ff390338d45f8e55969a04e65d76258c9::marketplace_scripts::purchase_many';

export async function listNFTs(pvkey: string, functionArguments: any) {
  try {
    const senderPvkey = new Ed25519PrivateKey(pvkey);
    const sender = Account.fromPrivateKey({ privateKey: senderPvkey });
    const transaction = await aptos.transaction.build.simple({
      sender: sender.accountAddress,
      data: {
        function:
          '0x584b50b999c78ade62f8359c91b5165ff390338d45f8e55969a04e65d76258c9::coin_listing::init_fixed_price_many',
        functionArguments: functionArguments,
        typeArguments: ['0x1::aptos_coin::AptosCoin'],
      },
    });

    const transactionRes = await aptos.transaction.signAndSubmitTransaction({
      signer: sender,
      transaction,
    });

    await aptos.waitForTransaction({ transactionHash: transactionRes.hash });
    return transactionRes.hash;
  } catch (error: Error | any) {
    throw error;
  }
}

export async function single_list_txn_builder(sender: string, token_id: string, price: number) {
  try {
    return await aptos.transaction.build.simple({
      sender: sender,
      data: {
        function:
          '0x584b50b999c78ade62f8359c91b5165ff390338d45f8e55969a04e65d76258c9::coin_listing::init_fixed_price',
        functionArguments: [
          token_id,
          '0x71f7c94805c33d32a7f9560c95f02e9d3b5bc49884a883916f03abe6da11ac08', // fee schedule
          price,
        ],
        typeArguments: ['0x1::aptos_coin::AptosCoin'],
      },
    });
  } catch (error) {
    console.log(error);
  }
}

export async function buyNFTs(pvkey: string, functionArguments: any) {
  try {
    const senderPvkey = new Ed25519PrivateKey(pvkey);
    const sender = Account.fromPrivateKey({ privateKey: senderPvkey });

    const transaction = await aptos.transaction.build.simple({
      sender: sender.accountAddress,
      data: {
        function: BUY_NFTS,
        functionArguments: functionArguments,
        typeArguments: ['0x1::aptos_coin::AptosCoin'],
      },
      options: {
        maxGasAmount: 800,
      },
    });

    const transactionRes = await aptos.transaction.signAndSubmitTransaction({
      signer: sender,
      transaction,
    });

    await aptos.waitForTransaction({ transactionHash: transactionRes.hash });
    return transactionRes.hash;
  } catch (error: Error | any) {
    throw error;
  }
}

import Panora, { PanoraConfig } from '@panoraexchange/swap-sdk';
const PANORA_API_KEY =
  process.env.PANORA_API_KEY || 'a4^KV_EaTf4MW#ZdvgGKX#HUD^3IFEAOV_kzpIE^3BQGA8pDnrkT7JcIy#HNlLGi';
import {
  Account,
  Aptos,
  AptosConfig,
  Ed25519PrivateKey,
  InputEntryFunctionData,
  Network,
  PrivateKey,
  PrivateKeyVariants,
  UserTransactionResponse,
} from '@aptos-labs/ts-sdk';
import { getTokenDecimals } from '@/db/indexer/snipe';
import axios from 'axios';
import { sendTransaction } from './tradeTransactions';

const { NETWORK, COIN_API } = process.env;

if (!NETWORK) {
  throw new Error('Network not set');
}
const aptosConfig = new AptosConfig({
  network: NETWORK.toLowerCase() === 'mainnet' ? Network.MAINNET : Network.TESTNET,
});

const aptos = new Aptos(aptosConfig);
const config: PanoraConfig = {
  apiKey: PANORA_API_KEY,
  rpcUrl: 'https://fullnode.mainnet.aptoslabs.com/v1',
};

const client = new Panora(config);

export interface SwapParams {
  fromTokenAddress: `0x${string}`;
  toTokenAddress: `0x${string}`;
  fromTokenAmount: string;
  toWalletAddress: `0x${string}`;
  slippagePercentage: string;
}

export const swapTokens = async (
  {
    fromTokenAddress,
    toTokenAddress,
    fromTokenAmount,
    toWalletAddress,
    slippagePercentage = '1',
  }: SwapParams,
  signerPvkey: string,
) => {
  try {
    const response = await client.Swap(
      {
        chainId: '1',
        fromTokenAddress,
        toTokenAddress,
        fromTokenAmount,
        toWalletAddress,
        slippagePercentage,
        integratorFeeAddress: '0xb68aad7770a6a3a63162898ddd4d9efedc970cca1404d65d24c867f7393beadd',
        integratorFeePercentage: '1.5',
      },
      signerPvkey,
      true,
    );
    const transaction = response as UserTransactionResponse;

    return {
      success: true,
      hash: transaction.hash,
      sender: transaction.sender,
      timestamp: new Date().toISOString(),
    };
  } catch (error: Error | any) {
    console.log('Error in swapTokens: \n Inputs: \n', {
      fromTokenAddress,
      toTokenAddress,
      fromTokenAmount,
      toWalletAddress,
    });
    throw error;
  }
};

async function performSwapRequest(swaptokenParams: SwapParams, apiKey: string) {
  const { fromTokenAddress, toTokenAddress, fromTokenAmount, toWalletAddress, slippagePercentage } =
    swaptokenParams;
  const endPoint = 'https://api.panora.exchange/swap';

  const queryString = new URLSearchParams({
    fromTokenAddress,
    toTokenAddress,
    toWalletAddress,
    fromTokenAmount: fromTokenAmount.toString(),
  }).toString();

  const url = `${endPoint}?${queryString}`;

  const headers = {
    'x-api-key': apiKey,
  };

  try {
    const res = await fetch(url, {
      method: 'POST',
      headers,
    });

    if (!res.ok) {
      throw new Error(`Request failed with status ${res.status}`);
    }

    const data = await res.json();
    return data;
  } catch (error) {
    console.error('Swap request error:', error);
    throw error;
  }
}

export const swapTokensWithPanoraApi = async (
  {
    fromTokenAddress,
    toTokenAddress,
    fromTokenAmount,
    toWalletAddress,
    slippagePercentage = '1',
  }: SwapParams,
  pvkey: string,
) => {
  try {
    const response = await performSwapRequest(
      {
        fromTokenAddress,
        toTokenAddress,
        fromTokenAmount,
        toWalletAddress,
        slippagePercentage,
      },
      PANORA_API_KEY,
    );
    const toTokenAmount = response.quotes[0].toTokenAmount;
    const account = Account.fromPrivateKey({
      privateKey: new Ed25519PrivateKey(
        PrivateKey.formatPrivateKey(pvkey, PrivateKeyVariants.Ed25519),
      ),
    });
    const txData = response?.quotes[0]?.txData;
    if (txData.arguments.length === 0) {
      throw new Error('No arguments found in txData');
    }
    const payload: InputEntryFunctionData = {
      function: txData.function,
      typeArguments: txData.type_arguments,
      functionArguments: txData.arguments,
    };
    const txResponse = await sendTransaction(account, payload);
    return {
      success: txResponse.success,
      hash: txResponse.hash,
      sender: toWalletAddress,
      timestamp: new Date().toISOString(),
      vm_status: txResponse.vm_status,
      toTokenAmount,
    };
  } catch (error: Error | any) {
    console.log('Error in swapTokensWithApi:\nInputs:', {
      fromTokenAddress,
      toTokenAddress,
      fromTokenAmount,
      toWalletAddress,
      slippagePercentage,
    });
    throw error;
  }
};

export const swapTokensTest = async (
  {
    fromTokenAddress,
    toTokenAddress,
    fromTokenAmount,
    toWalletAddress,
    slippagePercentage = '1',
  }: SwapParams,
  pvkey: string,
  quote: string,
) => {
  try {
    let swap_function: `${string}::${string}::${string}` =
      '0x3056f2988d441f856efe4dacc3527138ad6c40f0cea7c11af7ccab4a1db86969::router::swap_route_entry_to_coin';
    let args = [
      (Number(fromTokenAmount) * 10 ** 8).toString(),
      quote,
      fromTokenAddress,
      [toTokenAddress],
      ['false'],
      toWalletAddress,
    ];
    if (!fromTokenAddress) {
      args = [
        (Number(fromTokenAmount) * 10 ** 8).toString(),
        quote,
        [toTokenAddress],
        ['false'],
        toWalletAddress,
      ];
      swap_function =
        '0x3056f2988d441f856efe4dacc3527138ad6c40f0cea7c11af7ccab4a1db86969::router::swap_route_entry_from_coin';
    }
    const senderPvkey = new Ed25519PrivateKey(pvkey);
    const sender = Account.fromPrivateKey({ privateKey: senderPvkey });
    const typeArguments = ['0x1::aptos_coin::AptosCoin'];
    const buildTransaction = await aptos.transaction.build.simple({
      sender: sender.accountAddress,
      data: {
        function: swap_function,
        functionArguments: args,
        typeArguments: typeArguments,
      },
      options: {
        maxGasAmount: 800,
      },
    });
    const transactionRes = await aptos.transaction.signAndSubmitTransaction({
      signer: sender,
      transaction: buildTransaction,
    });
    const committedTransaction = await aptos.waitForTransaction({
      transactionHash: transactionRes.hash,
    });
    const transaction = committedTransaction as UserTransactionResponse;

    return {
      success: transaction.success,
      hash: transaction.hash,
      sender: transaction.sender,
      timestamp: transaction.timestamp.toString(),
    };
  } catch (error: Error | any) {
    console.log('Error in swapTokensTest:', error);
    throw error;
  }
};

export const transferTokens = async (
  amount: string,
  asset_address: string,
  toAddress: string,
  pvkey: string,
) => {
  try {
    const senderPvkey = new Ed25519PrivateKey(pvkey);
    const account = Account.fromPrivateKey({ privateKey: senderPvkey });
    const decimals = await getTokenDecimals(asset_address);
    if (!decimals) throw new Error('No data found for token Address');
    amount = (Number(amount) * 10 ** decimals).toString();
    
    let data: any = {
      function: '0x1::primary_fungible_store::transfer',
      functionArguments: [asset_address, toAddress, amount],
      typeArguments: ['0x1::fungible_asset::Metadata'],
    };
    
    if (asset_address?.includes('::')) {
      data = {
        function: '0x1::aptos_account::transfer_coins',
        functionArguments: [toAddress, amount],
        typeArguments: [asset_address],
      };
    }

    // First, build the transaction for simulation
    const transaction = await aptos.transaction.build.simple({
      sender: account.accountAddress,
      data,
    });

    // Simulate to get gas estimates
    const [userTransactionResponse] = await aptos.transaction.simulate.simple({
      signerPublicKey: account.publicKey,
      transaction,
      options: {
        estimateGasUnitPrice: true,
        estimateMaxGasAmount: true,
        estimatePrioritizedGasUnitPrice: true,
      },
    });

    // Set minimum gas amounts with safety margins
    const MIN_GAS_UNITS = 1000; // Minimum gas units for Aptos transactions
    const SAFETY_MULTIPLIER = 1.5; // 50% safety margin
    
    const estimatedGas = Number(userTransactionResponse.max_gas_amount);
    const gasUnitPrice = Number(userTransactionResponse.gas_unit_price);
    
    // Ensure we meet minimum requirements with safety margin
    const maxGasAmount = Math.max(
      MIN_GAS_UNITS, 
      Math.ceil(estimatedGas * SAFETY_MULTIPLIER)
    );

    // Build the final transaction with proper gas configuration
    const newTransaction = await aptos.transaction.build.simple({
      sender: account.accountAddress,
      data,
      options: {
        maxGasAmount: maxGasAmount,
        gasUnitPrice: Math.max(gasUnitPrice, 100), // Ensure minimum gas price
      },
    });

    // Sign and submit
    const transactionRes = await aptos.signAndSubmitTransaction({
      signer: account,
      transaction: newTransaction,
    });

    // Wait for confirmation
    const { success, hash } = await aptos.waitForTransaction({
      transactionHash: transactionRes.hash,
    });

    return { success, hash };
  } catch (error: Error | any) {
    console.log('Error in transferTokens:', error);
    
    // Provide more detailed error information
    if (error.message?.includes('MAX_GAS_UNITS_BELOW_MIN_TRANSACTION_GAS_UNITS')) {
      throw new Error('Transaction gas amount is below minimum required. Please increase gas limit.');
    }
    
    throw error;
  }
};

interface BalanceResponse {
  amount: number;
  assetType: string;
  assetMetadata: {
    decimals: number;
  };
  usdPrice: number;
  percentageChange24h: number;
}

export async function fetchUserBalance(
  ownerAddress: string,
  assetType: string,
): Promise<{ amount: number } | null> {
  try {
    const url = `${COIN_API}/balance/${ownerAddress}/${assetType}`;
    const response = await axios.get<BalanceResponse>(url);
    const { amount, assetMetadata } = response.data;
    const readableAmount = amount / Math.pow(10, assetMetadata.decimals);

    return { amount: readableAmount };
  } catch (error: Error | any) {
    console.error(error.message || error);
    return { amount: 0 };
  }
}

type PriceResponse = {
  assetType: string;
  usdPrice: number;
};

export async function getAssetUsdPrice(assetType: string): Promise<PriceResponse> {
  // const encodedAssetType = encodeURIComponent(assetType);
  const url = `${COIN_API}/${assetType}/price/usd`;

  try {
    const response = await axios.get<PriceResponse>(url);
    return response.data;
  } catch (error: any) {
    console.error('Error fetching USD price:', error?.response?.data || error.message);
    throw new Error('Failed to fetch USD price');
  }
}

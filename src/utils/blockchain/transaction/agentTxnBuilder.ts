import {
  Account,
  Aptos,
  AptosConfig,
  Ed25519PrivateKey,
  Network,
  PrivateKey,
  PrivateKeyVariants,
} from '@aptos-labs/ts-sdk';

const config = new AptosConfig({
  network: Network.TESTNET,
});
const aptos = new Aptos(config);
export async function buyAgentTokens(
  pvkey: string,
  reserve_address: string,
  amount: number,
  contract_address: string,
) {
  try {
    const senderPvkey = new Ed25519PrivateKey(
      PrivateKey.formatPrivateKey(pvkey, PrivateKeyVariants.Ed25519),
    );
    const sender = Account.fromPrivateKey({ privateKey: senderPvkey });
    const transaction = await aptos.transaction.build.simple({
      sender: sender.accountAddress,
      data: {
        function: `${contract_address}::agent::buy_tokens`,
        functionArguments: [amount, reserve_address],
        typeArguments: [],
      },
      options: {
        maxGasAmount: 800,
      },
    });

    const transactionRes = await aptos.transaction.signAndSubmitTransaction({
      signer: sender,
      transaction,
    });

    await aptos.waitForTransaction({ transactionHash: transactionRes.hash });
    return transactionRes.hash;
  } catch (error: Error | any) {
    console.log(error);
    throw error;
  }
}

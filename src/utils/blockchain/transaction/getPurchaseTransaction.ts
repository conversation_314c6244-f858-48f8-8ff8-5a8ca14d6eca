import axios from 'axios';

interface TransactionPayload {
  function: string;
  type_arguments: string[];
  arguments: string[];
}

interface TransactionResponse {
  sender: string;
  payload: TransactionPayload;
  success: boolean;
}

interface TransactionDetails {
  sender: string;
  receiver: string;
  amount: number;
  success: boolean;
}

export default async function getTransactionDetails(
  txnHash: string,
): Promise<TransactionDetails | null> {
  const apiUrl = `${process.env.EXPLORER_API}/v1/transactions/by_hash/${txnHash}`;

  try {
    const response = await axios.get<TransactionResponse>(apiUrl);

    return {
      sender: response.data.sender,
      receiver: response.data.payload.arguments[0], // The first argument in payload is the receiver
      amount: parseInt(response.data.payload.arguments[1]) / 1e8, // Convert from octas to APT
      success: response.data.success, // Returns true if successful, false otherwise
    };
  } catch (error) {
    console.error('Error fetching transaction:', error);
    return null; // Return null if the transaction cannot be retrieved
  }
}

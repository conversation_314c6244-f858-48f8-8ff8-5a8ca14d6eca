import {
  fromNumber,
  MerkleClient,
  MerkleClientConfig,
  calcPriceImpactInfo,
} from '@merkletrade/ts-sdk';
import {
  Account,
  Aptos,
  Ed25519PrivateKey,
  InputEntryFunctionData,
  PrivateKey,
  PrivateKeyVariants,
  UserTransactionResponse,
} from '@aptos-labs/ts-sdk';
const { NETWORK } = process.env;

if (!NETWORK) {
  throw new Error('Network not set');
}
// const config = new AptosConfig({
//   network: NETWORK.toLowerCase() === "mainnet" ? Network.MAINNET : Network.TESTNET,
// });
let merkle: MerkleClient;
(async () => {
  if (NETWORK === 'mainnet') {
    merkle = new MerkleClient(await MerkleClientConfig.mainnet());
  } else {
    merkle = new MerkleClient(await MerkleClientConfig.testnet());
  }
})();

export async function sendTransaction(account: Account, payload: InputEntryFunctionData) {
  try {
    const aptos = new Aptos(merkle.config.aptosConfig);
    const transaction = await aptos.transaction.build.simple({
      sender: account.accountAddress,
      data: payload,
    });

    const [userTransactionResponse] = await aptos.transaction.simulate.simple({
      signerPublicKey: account.publicKey,
      transaction,
      options: {
        estimateGasUnitPrice: true,
        estimateMaxGasAmount: true,
        estimatePrioritizedGasUnitPrice: true,
      },
    });
    if (!userTransactionResponse.success) {
      throw new Error(userTransactionResponse.vm_status);
    }
    const newTransaction = await aptos.transaction.build.simple({
      sender: account.accountAddress,
      data: payload,
      options: {
        maxGasAmount: Number(userTransactionResponse.max_gas_amount),
        gasUnitPrice: Number(userTransactionResponse.gas_unit_price),
      },
    });
    const { hash } = await aptos.signAndSubmitTransaction({
      signer: account,
      transaction: newTransaction,
    });
    return await aptos.waitForTransaction({ transactionHash: hash });
  } catch (error) {
    throw error;
  }
}

export async function openMarketOrder(
  pvkey: string,
  pairtype: string,
  leverage: number,
  collateral: number,
  isLong: boolean,
) {
  try {
    console.log('open market order');
    if (leverage * collateral < 300) {
      throw new Error('Pay * Leverage must be at least $300');
    }
    const account = Account.fromPrivateKey({
      privateKey: new Ed25519PrivateKey(
        PrivateKey.formatPrivateKey(pvkey, PrivateKeyVariants.Ed25519),
      ),
    });
    if (NETWORK === 'testnet') {
      try {
        const faucetPayload = merkle.payloads.testnetFaucetUSDC({
          amount: 10_000_000n,
        });
        await sendTransaction(account, faucetPayload);
      } catch (error) {
        console.log(error);
      }
    }

    await merkle.getPairInfo({ pairId: pairtype });
    console.log('pair info');
    const order = await merkle.payloads.placeMarketOrder({
      pair: pairtype,
      userAddress: account.accountAddress.toString(),
      sizeDelta: BigInt(Math.floor(collateral * leverage * 10 ** 6)),
      collateralDelta: BigInt(Math.floor(collateral * 10 ** 6)),
      isLong: isLong,
      isIncrease: true,
    }) as any;

    const committedTransaction = await sendTransaction(account, order);

    const transaction = committedTransaction as UserTransactionResponse;
    return {
      success: transaction.success,
      hash: transaction.hash,
      sender: account.accountAddress,
      timestamp: transaction.timestamp.toString(),
    };
  } catch (error: Error | any) {
    throw new Error(error.message || 'Error opening market order');
  }
}

export async function openLimitOrder(
  pvkey: string,
  pairtype: string,
  leverage: number,
  collateral: number,
  price: bigint,
  isLong: boolean,
) {
  try {
    if (leverage * collateral < 300) {
      throw new Error('Pay * Leverage must be at least $300');
    }
    const account = Account.fromPrivateKey({
      privateKey: new Ed25519PrivateKey(
        PrivateKey.formatPrivateKey(pvkey, PrivateKeyVariants.Ed25519),
      ),
    });
    if (NETWORK === 'testnet') {
      try {
        const faucetPayload = merkle.payloads.testnetFaucetUSDC({
          amount: 10_000_000n,
        });
        await sendTransaction(account, faucetPayload);
      } catch (error) {
        console.log(error);
      }
    }
    const order = await merkle.payloads.placeLimitOrder({
      pair: pairtype,
      userAddress: account.accountAddress.toString(),
      sizeDelta: BigInt(Math.floor(collateral * leverage * 10 ** 6)),
      collateralDelta: BigInt(Math.floor(collateral * 10 ** 6)),
      price: price,
      isLong: isLong,
      isIncrease: true,
    }) as any;

    // submit transaction

    const committedTransaction = await sendTransaction(account, order);

    const transaction = committedTransaction as UserTransactionResponse;
    return {
      success: transaction.success,
      hash: transaction.hash,
      sender: account.accountAddress,
      timestamp: transaction.timestamp.toString(),
    };
  } catch (error: Error | any) {
    throw new Error(error.message || 'Error opening limit order');
  }
}

export async function closePosition(pvkey: string, pairType: string, isLong: boolean) {
  try {
    const account = Account.fromPrivateKey({
      privateKey: new Ed25519PrivateKey(
        PrivateKey.formatPrivateKey(pvkey, PrivateKeyVariants.Ed25519),
      ),
    });

    const positions = await merkle.getPositions({
      address: account.accountAddress.toString(),
    });

    const position = positions.find(
      position => position.pairType.endsWith(pairType) && position.isLong === (isLong ?? true),
    );

    if (!position) {
      throw new Error('position not found');
    }
    // close position
    const closePayload = merkle.payloads.placeMarketOrder({
      pair: pairType,
      userAddress: account.accountAddress.toString(),
      sizeDelta: position.size,
      collateralDelta: position.collateral,
      isLong: position.isLong,
      isIncrease: false,
    }) as any;

    const committedTransaction = await sendTransaction(account, closePayload);
    const transaction = committedTransaction as UserTransactionResponse;
    return {
      success: transaction.success,
      hash: transaction.hash,
      sender: position.user,
      timestamp: transaction.timestamp.toString(),
    };
  } catch (error: Error | any) {
    throw new Error(error.message || 'Error closing position');
  }
}

export async function getPositions(pvkey: string) {
  try {
    const account = Account.fromPrivateKey({
      privateKey: new Ed25519PrivateKey(
        PrivateKey.formatPrivateKey(pvkey, PrivateKeyVariants.Ed25519),
      ),
    });
    const positions = await merkle.getPositions({
      address: account.accountAddress.toString(),
    });
    const stringfiedPositions = JSON.stringify(positions, (key, value) => {
      return typeof value === 'bigint' ? value.toString() : value;
    });
    return stringfiedPositions;
  } catch (error: Error | any) {
    throw new Error(error.message || 'Error getting positions');
  }
}

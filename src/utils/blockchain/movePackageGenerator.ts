import { execSync } from 'child_process';
import * as fs from 'fs';
import * as path from 'path';
import { v4 as uuidv4 } from 'uuid'; // Import UUID for unique naming

// Types and interfaces
interface Config {
  VALID_TOKEN_NAME_REGEX: RegExp;
  MAX_TOKEN_NAME_LENGTH: number;
}

interface PackagePaths {
  bytecode: string;
  metadata: string;
  moduleHex: string;
  metadataHex: string;
}

interface BytecodeResponse {
  moduleBytecode: string;
  metadataBytecode: string;
}

// Configuration object
const CONFIG: Config = {
  VALID_TOKEN_NAME_REGEX: /^[a-zA-Z][a-zA-Z0-9_]*$/,
  MAX_TOKEN_NAME_LENGTH: 64,
};

/**
 * Validates the token name against defined rules
 * @param tokenName - Name of the token to validate
 * @throws Error if validation fails
 */
const validateTokenName = (tokenName: string): void => {
  if (!tokenName || typeof tokenName !== 'string') {
    throw new Error('Token name must be a non-empty string');
  }
  if (!CONFIG.VALID_TOKEN_NAME_REGEX.test(tokenName)) {
    throw new Error(
      'Token name must start with a letter and contain only letters, numbers, and underscores',
    );
  }
  if (tokenName.length > CONFIG.MAX_TOKEN_NAME_LENGTH) {
    throw new Error(`Token name must not exceed ${CONFIG.MAX_TOKEN_NAME_LENGTH} characters`);
  }
};

/**
 * Creates a directory if it doesn't exist
 * @param dirPath - Path of the directory to create
 */
const ensureDirectoryExists = (dirPath: string): void => {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
};

/**
 * Executes a shell command with error handling
 * @param command - Command to execute
 * @param options - Optional execution options
 * @throws Error if command execution fails
 */
const executeCommand = (command: string, options: Record<string, unknown> = {}): void => {
  try {
    execSync(command, { stdio: 'inherit', ...options });
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Command failed: ${command}\n${error.message}`);
    }
    throw error;
  }
};

/**
 * Generates Move module code
 * @param tokenName - Name of the token
 * @returns Generated Move module code as string
 */
const generateMoveModule = (tokenName: string, walletAddress: string): string => {
  return `
module ${walletAddress}::${tokenName} {
    struct ${tokenName} {}
}
    `;
};

/**
 * Creates a Move package for the specified token
 * @param tokenName - Name of the token
 * @returns Promise that resolves when package creation is complete
 */
const createMovePackage = async (tokenName: string, walletAddress: string): Promise<string> => {
  const uniqueId = uuidv4();
  const tempDir = path.join(__dirname, `temp_${uniqueId}`);

  ensureDirectoryExists(tempDir);

  try {
    console.log(`Starting Move package creation for token: ${tokenName}`);
    validateTokenName(tokenName);

    const sourcesDir = path.join(tempDir, 'sources');
    ensureDirectoryExists(sourcesDir);

    console.log('Initializing Move package...');
    executeCommand(`aptos move init --name ${tokenName}`, { cwd: tempDir, stdio: 'inherit' });

    console.log('Generating Move module...');
    const moduleCode = generateMoveModule(tokenName, walletAddress);

    fs.writeFileSync(path.join(sourcesDir, `${tokenName}.move`), moduleCode, 'utf8');

    console.log('Compiling Move package...');
    executeCommand(`aptos move compile --package-dir ${tempDir} --save-metadata`, {
      stdio: 'inherit',
    });

    console.log('Package creation completed successfully!');
    return tempDir;
  } catch (error) {
    throw new Error(
      `Failed to create Move package: ${error instanceof Error ? error.message : 'Unknown error'}`,
    );
  }
};

/**
 * Generates bytecode and returns it as strings, then cleans up files
 * @param tokenName - Name of the token
 * @returns Promise that resolves with bytecode strings
 */
const generateAndGetBytecode = async (
  tokenName: string,
  walletAddress: string,
): Promise<BytecodeResponse> => {
  const tempDir = await createMovePackage(tokenName, walletAddress);

  try {
    console.log('Generating hex files...');
    const buildDir = path.join(tempDir, 'build', tokenName);

    const paths: PackagePaths = {
      bytecode: path.join(buildDir, 'bytecode_modules', `${tokenName}.mv`),
      metadata: path.join(buildDir, 'package-metadata.bcs'),
      moduleHex: path.join(buildDir, 'module.hex'),
      metadataHex: path.join(buildDir, 'metadata.hex'),
    };

    if (!fs.existsSync(paths.bytecode) || !fs.existsSync(paths.metadata)) {
      throw new Error('Required compiled files not found');
    }

    executeCommand(`xxd -p "${paths.bytecode}" | tr -d '\n' > "${paths.moduleHex}"`);
    executeCommand(`xxd -p "${paths.metadata}" | tr -d '\n' > "${paths.metadataHex}"`);

    const moduleBytecode = `0x${fs.readFileSync(paths.moduleHex, 'utf8')}`;
    const metadataBytecode = `0x${fs.readFileSync(paths.metadataHex, 'utf8')}`;

    console.log('Bytecode generated successfully.');
    return { moduleBytecode, metadataBytecode };
  } finally {
    console.log('Cleaning up temporary files...');
    fs.rmSync(tempDir, { recursive: true, force: true });
  }
};

export { generateAndGetBytecode, validateTokenName };

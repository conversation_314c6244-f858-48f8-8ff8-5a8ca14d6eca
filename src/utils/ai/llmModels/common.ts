export const classifyCryptoAction = (input: string): any => {
  try {
    // Trim leading/trailing whitespace
    let trimmed = input.trim();

    // If the entire string starts and ends with single quotes, remove them
    if (trimmed.startsWith("'") && trimmed.endsWith("'")) {
      trimmed = trimmed.slice(1, -1);
    }

    // Now we can parse it as normal JSON
    return JSON.parse(trimmed);
  } catch (error: any) {
    console.error('Failed to parse JSON:', error.message);
    return null;
  }
};

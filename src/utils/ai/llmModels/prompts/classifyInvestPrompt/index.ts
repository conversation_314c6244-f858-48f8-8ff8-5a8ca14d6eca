import intentDefs from './intents';
import tokenList from './tokens';

interface ParsedIntent {
  budget?: number;
  profitTarget?: number;
}

interface IntentRule {
  intent: string;
  patterns?: RegExp[];
  keywords?: string[];
}

const INTENT_RULES: IntentRule[] = intentDefs.map((def) => ({
  intent: def.intent,
  patterns: def.patterns?.map((pat) => new RegExp(pat, 'ig')),
  keywords: def.keywords?.map((kw) => kw.toLowerCase())
}));

// classifyText: returns only the intent as a string
export const classifyText = (prompt: string): string => {
  const normalized = prompt.toLowerCase().replace(/\s+/g, ' ').trim();
  for (const rule of INTENT_RULES) {
    // Pattern matching
    if (rule.patterns) {
      for (const pattern of rule.patterns) {
        pattern.lastIndex = 0;
        let match: RegExpExecArray | null;
        while ((match = pattern.exec(normalized)) !== null) {
          if (rule.intent === 'profit_invest') {
            // Only return intent
            return rule.intent;
          }
        }
      }
    }
    // Keyword matching
    if (rule.keywords) {
      const matchedAny = rule.keywords.some((kw) => normalized.includes(kw));
      if (matchedAny) {
        return rule.intent;
      }
    }
  }
  return 'default_fallback';
};

// parsePrompt: returns intent, profitTarget, budget, etc.
export const parsePrompt = (prompt: string): ParsedIntent => {
    const normalized = prompt
    .toLowerCase()
    .replace(/\s+/g, ' ')
    .replace(/\$/g, '')
    .trim();
  
    for (const rule of INTENT_RULES) {
    // Pattern matching (regex)
    if (rule.patterns) {
      for (const pattern of rule.patterns) {
        pattern.lastIndex = 0;
        let match: RegExpExecArray | null;
        while ((match = pattern.exec(normalized)) !== null) {
          if (rule.intent === 'profit_invest') {
            const profit = parseFloat(match[1]);
            const budget = parseFloat(match[2]);
            if (!isNaN(profit) && !isNaN(budget)) {
              return {
                profitTarget: profit,
                budget
              };
            }
          }
        }
      }
    }
  }
  return {
    budget: undefined,
    profitTarget: undefined
  };
}

export default parsePrompt;
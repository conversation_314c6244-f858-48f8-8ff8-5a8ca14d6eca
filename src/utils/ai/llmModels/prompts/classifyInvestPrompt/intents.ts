const intents = [
  {
    "intent": "profit_invest",
    "patterns": [
  "(?:i want to\\s*)?make\\s*\\$?(\\d+(?:\\.\\d+)?)\\s*(?:dollars|usd|profit)?\\s*by investing\\s*\\$?(\\d+(?:\\.\\d+)?)(?:\\s*(?:dollars|usd)?)?",
  "earn\\s*\\$?(\\d+(?:\\.\\d+)?)\\s*(?:profit)?\\s*with an investment of\\s*\\$?(\\d+(?:\\.\\d+)?)(?:\\s*(?:usd|dollars)?)?",
  "how can i make\\s*\\$?(\\d+(?:\\.\\d+)?)\\s*by putting in\\s*\\$?(\\d+(?:\\.\\d+)?)",
  "plan to achieve\\s*\\$?(\\d+(?:\\.\\d+)?)\\s*(?:profit)?\\s*using\\s*\\$?(\\d+(?:\\.\\d+)?)",
  "help me make\\s*\\$?(\\d+(?:\\.\\d+)?)\\s*(?:profit)?\\s*with\\s*\\$?(\\d+(?:\\.\\d+)?)",
  "i aim to earn\\s*\\$?(\\d+(?:\\.\\d+)?)\\s*by investing\\s*\\$?(\\d+(?:\\.\\d+)?)",
  "my goal is to get\\s*\\$?(\\d+(?:\\.\\d+)?)\\s*(?:profit)?\\s*from\\s*\\$?(\\d+(?:\\.\\d+)?)",
  "targeting\\s*\\$?(\\d+(?:\\.\\d+)?)\\s*(?:gain|profit)?\\s*with\\s*\\$?(\\d+(?:\\.\\d+)?)",
  "expecting\\s*\\$?(\\d+(?:\\.\\d+)?)\\s*(?:profit)?\\s*on\\s*\\$?(\\d+(?:\\.\\d+)?)",
  "planning\\s*\\$?(\\d+(?:\\.\\d+)?)\\s*return with\\s*\\$?(\\d+(?:\\.\\d+)?)",
  "make\\s+\\$?(\\d+(?:\\.\\d+)?)\\s+(?:in )?profit.*?investing\\s+\\$?(\\d+(?:\\.\\d+)?)",
  "want to.*?\\$?(\\d+(?:\\.\\d+)?)\\s*(?:in )?profit.*?investing\\s+\\$?(\\d+(?:\\.\\d+)?)"
],
  "keywords": ["make", "profit", "invest", "earn", "gain", "return", "target"]
  },
]

export default intents;
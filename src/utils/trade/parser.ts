import fs from 'fs';
import path from 'path';

interface ParsedIntent {
  intent: string;
  budget?: number;
  profitTarget?: number;
  tokenName?: string;
  tokenNames?: string[];
  message?: string;
}

interface IntentRule {
  intent: string;
  patterns?: RegExp[];
  keywords?: string[];
}

// 1️⃣ Load intents.json dynamically
const intentsPath = path.join(__dirname, 'intents.json');
const intentsRaw = fs.readFileSync(intentsPath, 'utf-8');
const intentDefs = JSON.parse(intentsRaw) as {
  intent: string;
  patterns?: string[];
  keywords?: string[];
  fallback?: boolean;
}[];

const INTENT_RULES: IntentRule[] = intentDefs.map((def) => ({
  intent: def.intent,
  patterns: def.patterns?.map((pat) => new RegExp(pat, 'ig')),
  keywords: def.keywords?.map((kw) => kw.toLowerCase())
}));

// 2️⃣ Load token list
const tokensPath = path.join(__dirname, 'tokens.json');
const tokensRaw = fs.readFileSync(tokensPath, 'utf-8');
const TOKEN_LIST = JSON.parse(tokensRaw) as string[];

export function parsePrompt(prompt: string): ParsedIntent {
  const normalized = prompt.toLowerCase().replace(/\s+/g, ' ').trim();
  const foundTokens: Set<string> = new Set();

  for (const rule of INTENT_RULES) {
    // 1️⃣ Pattern matching (regex)
    if (rule.patterns) {
      for (const pattern of rule.patterns) {
        pattern.lastIndex = 0;
        let match: RegExpExecArray | null;

        while ((match = pattern.exec(normalized)) !== null) {
          if (rule.intent === 'profit_invest') {
            const profit = parseFloat(match[1]);
            const budget = parseFloat(match[2]);
            if (!isNaN(profit) && !isNaN(budget)) {
              return {
                intent: rule.intent,
                profitTarget: profit,
                budget
              };
            }
          }

          if (rule.intent === 'profit_only') {
            const profit = parseFloat(match[1]);
            if (!isNaN(profit)) {
              return {
                intent: rule.intent,
                profitTarget: profit
              };
            }
          }

          if (rule.intent === 'invest_only') {
            const budget = parseFloat(match[1]);
            if (!isNaN(budget)) {
              return {
                intent: rule.intent,
                budget
              };
            }
          }

          if (rule.intent === 'token_query') {
            let token = match[1]?.toUpperCase();
            if (TOKEN_LIST.includes(token)) {
              foundTokens.add(token);
            }
          }
        }
      }

      if (rule.intent === 'token_query' && foundTokens.size > 0) {
        const tokensArray = Array.from(foundTokens);
        return {
          intent: rule.intent,
          tokenName: tokensArray[0],
          tokenNames: tokensArray
        };
      }
    }

    // 2️⃣ Keyword matching (any match)
    if (rule.keywords) {
      const matchedAny = rule.keywords.some((kw) => normalized.includes(kw));
      if (matchedAny) {
        return { intent: rule.intent };
      }
    }
  }

  // 3️⃣ Fallback
  const fallbackIntent = intentDefs.find((d) => d.fallback === true);
  return {
    intent: fallbackIntent?.intent || 'default_fallback',
    message: 'How Much Profit Would You Like To Make Today?'
  };
}
// aiStorage.ts — local store for tracking AI agent-opened positions
import fs from 'fs';
import path from 'path';

const STORAGE_PATH = 'ai_positions.json';

export interface StoredPosition {
  leverage: number;
  symbol: string;
  entryPrice: number;
  txHash: string;
  timestamp: number;
  signalScore: number;
  marketRegime: string;
  tp: number;
  sl: number;
  rsi: number;
  macdHist: number;
  emaSlope: number;
  atrPct: number;
  breakevenActivated?: boolean;   // ✅ NEW
  trailingTPPct?: number;         // ✅ NEW
}

export function loadAIPOS(): StoredPosition[] {
  try {
    const raw = fs.readFileSync(STORAGE_PATH, 'utf-8');
    return JSON.parse(raw) as StoredPosition[];
  } catch {
    return [];
  }
}

export function saveAIPOS(data: StoredPosition[]) {
  fs.writeFileSync(STORAGE_PATH, JSON.stringify(data, null, 2));
}

export function recordAIPOS(
  symbol: string,
  entry: number,
  txHash: string,
  score: number,
  regime: string,
  leverage: number,
  tp: number,
  sl: number,
  rsi: number,
  macdHist: number,
  emaSlope: number,
  atrPct: number,
  breakevenActivated = false,
  trailingTPPct = 0
) {
  const data = loadAIPOS();
  const updated: StoredPosition = {
    symbol,
    entryPrice: entry,
    txHash,
    signalScore: score,
    marketRegime: regime,
    leverage,
    tp,
    sl,
    rsi,
    macdHist,
    emaSlope,
    atrPct,
    timestamp: Date.now(),
    breakevenActivated,
    trailingTPPct
  };

  const withoutOld = data.filter(p => !(p.symbol === symbol && Math.abs(p.entryPrice - entry) < 0.005));
  withoutOld.push(updated);
  saveAIPOS(withoutOld);
}

export function getAIPOS(symbol: string): StoredPosition | undefined {
  const all = loadAIPOS();
  return all.find(p => p.symbol === symbol);
}

export function setAIPOS(symbol: string, updated: StoredPosition) {
  const all = loadAIPOS();
  const withoutOld = all.filter(p => p.symbol !== symbol);
  withoutOld.push(updated);
  saveAIPOS(withoutOld);
}

export function removeAIPOS(symbol: string, entryPrice: number, tolerance = 0.01) {
  const existing = loadAIPOS();
  const filtered = existing.filter(
    pos => pos.symbol !== symbol || Math.abs(pos.entryPrice - entryPrice) >= tolerance
  );
  saveAIPOS(filtered);
}
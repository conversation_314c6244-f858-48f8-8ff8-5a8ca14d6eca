// tradeLogger.ts — CSV + DB logger for executed trades
import fs from 'fs';
import path from 'path';

const LOG_PATH = path.join(__dirname, 'trade_logs.csv');

// 🧾 CSV Header
const CSV_HEADER = [
  'timestamp',
  'event',
  'symbol',
  'direction',
  'entryPrice',
  'exitPrice',
  'leverage',
  'pnlPct',
  'signalScore',
  'marketRegime',
  'rsi',
  'macdHist',
  'emaSlope',
  'atrPct',
  'result',
  'note',
  'txHash'
].join(',');

// 📁 Ensure header is written once
if (!fs.existsSync(LOG_PATH)) {
  fs.writeFileSync(LOG_PATH, CSV_HEADER + '\n');
}

// 📦 Log input structure
export type LogTradeInput = {
  event: 'open' | 'close';
  symbol: string;
  direction: 'long' | 'short';
  entryPrice: number;
  exitPrice: number;
  leverage: number;
  pnlPct: number;
  signalScore: number;
  marketRegime: string;
  rsi: number;
  macdHist: number;
  emaSlope: number;
  atrPct: number;
  result?: 'win' | 'loss' | 'breakeven';
  note?: string;
  txHash?: string;
};

/**
 * Logs a trade to both CSV and DB
 */
export async function logTrade(input: LogTradeInput, saveToDB = true) {
  const {
    event,
    symbol,
    direction,
    entryPrice,
    exitPrice,
    leverage,
    pnlPct,
    signalScore,
    marketRegime,
    rsi,
    macdHist,
    emaSlope,
    atrPct,
    result = '',
    note = '',
    txHash = ''
  } = input;

  const timestamp = new Date().toISOString();

  const row = [
    timestamp,
    event,
    symbol,
    direction,
    entryPrice.toFixed(6),
    exitPrice.toFixed(6),
    leverage.toFixed(0),
    pnlPct.toFixed(2),
    signalScore.toFixed(2),
    marketRegime,
    rsi.toFixed(2),
    macdHist.toFixed(5),
    emaSlope.toFixed(5),
    atrPct.toFixed(3),
    result,
    `"${note}"`,
    txHash
  ].join(',');

  fs.appendFileSync(LOG_PATH, row + '\n');
  console.log('✅ Trade logged:', row);

  // if (saveToDB) {
  //   try {
  //     await db.insert(trades).values({
  //       symbol,
  //       direction: direction.toUpperCase(),     // 'LONG' or 'SHORT'
  //       entryPrice,
  //       exitPrice,
  //       pnl: pnlPct,
  //       leverage,
  //       reason: note || '',
  //       txHash: txHash || '',
  //       isWin: result === 'win',
  //       entryTime: new Date(),
  //       exitTime: new Date()
  //     });
  //   } catch (err) {
  //     console.error('❌ Failed to insert trade into DB:', err);
  //   }
  // }
}
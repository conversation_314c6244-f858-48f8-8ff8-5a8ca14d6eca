// ✅ chatgpt.ts — enhanced with retry logic and GPT strategy
import dotenv from 'dotenv';
dotenv.config();

import OpenAI from 'openai';
import { priceFeeds } from '@/utils/trade/blockchain';

const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY! });

export async function generateStrategyResponse(params: { budget: number; profitTarget: number }): Promise<string> {
  
  const { budget, profitTarget } = params;

  const tradePrompt = `
    You are an AI trading strategist. Based on the user's intent to grow $${budget} into $${profitTarget}, write a structured trading strategy.

    Each section must begin with a bold Markdown heading (e.g., **Section Title**) on its own line, followed by one line break and a concise paragraph of content.

    Include the following five sections exactly, with these content guidelines:

    1. **Trading With A Fixed Budget And Profit Goal**  
      - Explain how the user's profit goal translates into a practical trading strategy, and outline the expected ROI path based on the initial investment of $${budget} targeting $${profitTarget}.

    2. **Token Pair Selection**  
      - Clarify that tokens are dynamically selected using Merkle Trade’s trending and top mover lists to capture active market flows. Use the placeholder \`{{TOKEN_PAIRS}}\` where the final token list will be inserted.

    3. **Budget Division And Leverage**  
      - Describe how the total budget is split into 2, 4, or 6 positions to manage risk and maximize opportunity. Explain how leverage is intelligently scaled depending on the required return.

    4. **Technical Indicator Analysis**  
      - Describe how Alura’s AI engine performs real-time technical analysis using indicators like EMA, RSI, ATR, and MACD to confirm entry signals and improve timing.

    5. **Strategy To Achieve Profit Goal**  
      - Start the paragraph with: “**In Order To Achieve The Profit Goal Of $${profitTarget}, the bot will...**” and then describe the execution strategy including risk management, monitoring, and exit conditions.

    Ensure each section is separated by one blank line. Use a professional, concise tone throughout.
`;

  const completion = await openai.chat.completions.create({
    model: 'gpt-4.1',
    messages: [
      {
        role: 'system',
        content: tradePrompt,
      },
      { role: 'user', content: 'Explain strategy based on budget and profit target.' }
    ],
    temperature: 0.85,
    max_tokens: 3500,
  });

  return completion.choices[0].message.content?.trim() || '⚠️ Could not generate a strategy.';
}

export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  retries = 3,
  delay = 3000
): Promise<T> {
  let attempt = 0;
  while (attempt <= retries) {
    try {
      return await fn();
    } catch (err) {
      attempt++;
      if (attempt > retries) throw err;
      console.warn(`⚠️ Retry ${attempt}/${retries} after ${delay}ms due to error: ${err}`);
      await new Promise(res => setTimeout(res, delay * attempt));
    }
  }
  throw new Error("Retries exhausted");
}

export async function getTrendingAndTopMoverTokens(): Promise<{
  trending: Array<{ id: string; chart?: number[] }>;
  topMovers: Array<{ id: string; chart?: number[] }>;
}> {
  const trendingRes = await retryWithBackoff(() => fetch('https://api.merkle.trade/v1/market/pair/trending'));
  const trendingData = await trendingRes.json();
  const validTrending = trendingData.pairs.filter((p: any) => priceFeeds.hasOwnProperty(p.id));

  const topRes = await retryWithBackoff(() => fetch('https://api.merkle.trade/v1/market/pair/top-mover'));
  const topData = await topRes.json();
  const validTopMovers = topData.pairs.filter((p: any) => priceFeeds.hasOwnProperty(p.id));

  return {
    trending: validTrending,
    topMovers: validTopMovers,
  };
}

export function parsePrompt(input: string): { budget: number; profitTarget: number } {
  const budgetMatch = input.match(/\$?(\d+)\s*(?:to invest|investment|budget|by investing|\b)/i);
  const profitMatch = input.match(/\$?(\d+)\s*(?:profit|goal)/i);

  const budget = budgetMatch ? parseFloat(budgetMatch[1]) : 100;
  const profitTarget = profitMatch ? parseFloat(profitMatch[1]) : 10;

  return { budget, profitTarget };
}
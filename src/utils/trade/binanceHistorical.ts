// binanceHistorical.ts
import fetch from 'node-fetch';

export interface OHLCV {
  open: number[];
  high: number[];
  low: number[];
  close: number[];
  volume: number[];
}

// ✅ Define a single candle type
export interface Candle {
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
}

const tokenSymbolToBinancePair: Record<string, string> = {
  BTC: 'BTCUSDT',
  ETH: 'ETHUSDT',
  ADA: 'ADAUSDT',
  XRP: 'XRPUSDT',
  NEIRO: 'NEIROUSDT',
  DOGS: 'DOGSUSDT',
  PNUT: 'PNUTUSDT',
  FLOKI: 'FLOKIUSDT',
  BOME: 'BOMEUSDT',
  LTC: 'LTCUSDT',
  DOGE: 'DOGEUSDT',
  EIGEN: 'EIGENUSDT',
  TAO: 'TAOUSDT',
  ZRO: 'ZROUSDT',
  OP: 'OPUSDT',
  SHIB: 'SHIBUSDT',
  BONK: 'BONKUSDT',
  HBAR: 'HBARUSDT',
  ENA: 'ENAUSDT',
  PEPE: 'PEPEUSDT',
  LINK: 'LINKUSDT',
  WIF: 'WIFUSDT',
  WLD: 'WLDUSDT',
  STRK: 'STRKUSDT',
  INJ: 'INJUSDT',
  MANTA: 'MANTAUSDT',
  SEI: 'SEIUSDT',
  AVAX: 'AVAXUSDT',
  BLUR: 'BLURUSDT',
  MEME: 'MEMEUSDT',
  TIA: 'TIAUSDT',
  BNB: 'BNBUSDT',
  MATIC: 'MATICUSDT',
  ARB: 'ARBUSDT',
  VIRTUAL: 'VIRTUALUSDT',
  JUP: 'JUPUSDT',
  PYTH: 'PYTHUSDT',
  W: 'WUSDT',
  SUI: 'SUIUSDT',
  TRUMP: 'TRUMPUSDT',
  SOL: 'SOLUSDT',
  APT: 'APTUSDT',
  FARTCOIN: 'FARTCOIN_USD',
  KAITO: 'KAITOUSDT',
  EOS: 'EOSUSDT',
};

/**
 * Fetch historical OHLCV data using Binance public API.
 * @param symbol Your internal symbol (e.g., BTC_USD, ETH_USD)
 * @param interval Binance interval (default '5m')
 * @param limit Number of candles (default 100)
 */
export async function fetchOHLCVFromBinance({
  symbol,
  interval = '5m',
  limit = 100,
}: {
  symbol: string;
  interval?: string;
  limit?: number;
}): Promise<OHLCV> {
  const cleanSymbol = symbol.replace('_USD', '');
  const binancePair = tokenSymbolToBinancePair[cleanSymbol];

  if (!binancePair || binancePair.includes('FARTCOIN') || binancePair.endsWith('_USD')) {
    console.warn(`⚠️ Skipping unsupported token: ${symbol}`);
    return {
      open: [],
      high: [],
      low: [],
      close: [],
      volume: [],
    };
  }  

  const url = `https://api.binance.com/api/v3/klines?symbol=${binancePair}&interval=${interval}&limit=${limit}`;
  console.log(`🔗 Fetching OHLCV from Binance: ${url}`);

  const response = await fetch(url);
  if (!response.ok) {
    throw new Error(`❌ Failed to fetch OHLCV: ${response.status} ${response.statusText}`);
  }

  const data = await response.json();
  if (!Array.isArray(data) || data.length === 0) {
    console.warn(`⚠️ No OHLCV data returned for ${symbol}`);
    return {
      open: [],
      high: [],
      low: [],
      close: [],
      volume: [],
    };
  }

  const ohlcv: OHLCV = {
    open: data.map((d: any) => parseFloat(d[1])),
    high: data.map((d: any) => parseFloat(d[2])),
    low: data.map((d: any) => parseFloat(d[3])),
    close: data.map((d: any) => parseFloat(d[4])),
    volume: data.map((d: any) => parseFloat(d[5])),
  };

  console.log(`✅ Successfully fetched ${ohlcv.close.length} candles for ${symbol}`);
  return ohlcv;
}

/**
 * Fetch the latest single candle (OHLCV) using Binance public API.
 * @param symbol Your internal symbol (e.g., BTC_USD, ETH_USD)
 * @param interval Binance interval (default '1m')
 */
export async function fetchSingleCandle(symbol: string, interval = '1m'): Promise<Candle | null> {
  const cleanSymbol = symbol.replace('_USD', '');
  const binancePair = tokenSymbolToBinancePair[cleanSymbol];

  if (!binancePair) {
    console.error(`❌ Binance pair not found for symbol: ${symbol} (looked up as ${cleanSymbol})`);
    return null;
  }

  const url = `https://api.binance.com/api/v3/klines?symbol=${binancePair}&interval=${interval}&limit=1`;
  console.log(`🔗 Fetching single candle from Binance: ${url}`);

  try {
    const response = await fetch(url);
    if (!response.ok) {
      console.error(`❌ Failed to fetch candle: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    if (!Array.isArray(data) || data.length === 0) {
      console.warn(`⚠️ No candle data returned for ${symbol}`);
      return null;
    }

    const [open, high, low, close, volume] = [
      parseFloat(data[0][1]),
      parseFloat(data[0][2]),
      parseFloat(data[0][3]),
      parseFloat(data[0][4]),
      parseFloat(data[0][5]),
    ];

    console.log(`✅ Fetched candle for ${symbol}: O=${open}, H=${high}, L=${low}, C=${close}, V=${volume}`);
    return { open, high, low, close, volume };
  } catch (err) {
    console.error(`❌ Error fetching single candle for ${symbol}:`, err);
    return null;
  }
}
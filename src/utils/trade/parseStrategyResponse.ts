type Section = {
  title: string;
  description: string;
};

type ParsedResponse = {
  matchedSection: Section | null;
  allSections: Section[];
  titles: string[];
};

/**
 * Parses a GPT response into titled sections and retrieves a specific section by its title.
 *
 * @param gptText - The raw response from GPT
 * @param titleQuery - The exact section title to retrieve (case-insensitive)
 * @param tokenPairs - Optional list of token pairs for {{TOKEN_PAIRS}} replacement
 */
export default function parseStrategyResponse(
  gptText: string,
  titleQuery: string,
  tokenPairs?: string[]
): ParsedResponse {
  const sections: Section[] = [];
  
  // First, convert escaped newlines to actual newlines and clean up the text
  let normalizedText = gptText.replace(/\\n/g, '\n');
  
  // Remove leading/trailing quotes if they exist
  normalizedText = normalizedText.replace(/^['"`]|['"`]$/g, '');
  
  // Split the text into potential sections using a more sophisticated approach
  // Look for **Title** patterns that are likely section headers
  const sectionRegex = /(?:^|\n\n+)\*\*([^*\n]+)\*\*(?:\n|$)/g;
  const sectionMatches: { title: string; startIndex: number; endIndex: number; fullMatch: string }[] = [];
  let match;
  
  while ((match = sectionRegex.exec(normalizedText)) !== null) {
    sectionMatches.push({
      title: match[1].trim(),
      startIndex: match.index,
      endIndex: match.index + match[0].length,
      fullMatch: match[0]
    });
  }
  
  // Process each section
  for (let i = 0; i < sectionMatches.length; i++) {
    const currentSection = sectionMatches[i];
    const nextSection = sectionMatches[i + 1];
    
    // Extract content between this section and the next (or end of text)
    const contentStart = currentSection.endIndex;
    const contentEnd = nextSection ? nextSection.startIndex : normalizedText.length;
    
    let content = normalizedText.substring(contentStart, contentEnd).trim();
    
    // Clean up the content - remove extra newlines at the start/end
    content = content.replace(/^\n+|\n+$/g, '');
    
    // Skip sections that are obviously not main section headers
    // (like inline bold text that got caught by our regex)
    const title = currentSection.title;
    
    // Check if this looks like a real section title vs inline bold text
    const isLikelySection = 
      // Not too long (inline text tends to be longer)
      title.length < 100 &&
      // Doesn't start with lowercase (section titles are typically capitalized)
      /^[A-Z]/.test(title) &&
      // Doesn't contain certain patterns that suggest it's inline text
      !title.toLowerCase().includes('in order to') &&
      !title.toLowerCase().includes('the bot will') &&
      // Has actual content following it or is at a section boundary
      (content.length > 10 || i === sectionMatches.length - 1);
    
    if (isLikelySection) {
      // Handle token pairs replacement
      if (title === 'Token Pair Selection' && tokenPairs && tokenPairs.length > 0) {
        const tokenString = tokenPairs.join(', ');
        content = content.replace(/\{\{TOKEN_PAIRS\}\}/g, tokenString);
      }
      
      sections.push({ title, description: content });
    } else if (sections.length > 0) {
      // If this doesn't look like a section header, append it to the previous section
      const lastSection = sections[sections.length - 1];
      lastSection.description += '\n\n**' + title + '** ' + content;
    }
  }
  
  // If no sections were found using the regex approach, try a simpler line-by-line approach
  if (sections.length === 0) {
    const lines = normalizedText.split('\n');
    let currentTitle = '';
    let currentContent: string[] = [];
    
    for (const line of lines) {
      const trimmedLine = line.trim();
      
      // Check if this line is a section title
      const titleMatch = trimmedLine.match(/^\*\*(.+)\*\*$/);
      
      if (titleMatch && trimmedLine.length < 100) {
        // Save previous section if it exists
        if (currentTitle) {
          let description = currentContent.join('\n').trim();
          
          // Handle token pairs replacement
          if (currentTitle === 'Token Pair Selection' && tokenPairs && tokenPairs.length > 0) {
            const tokenString = tokenPairs.join(', ');
            description = description.replace(/\{\{TOKEN_PAIRS\}\}/g, tokenString);
          }
          
          sections.push({ title: currentTitle, description });
        }
        
        // Start new section
        currentTitle = titleMatch[1].trim();
        currentContent = [];
      } else if (currentTitle) {
        // Add content to current section
        currentContent.push(line);
      }
    }
    
    // Don't forget the last section
    if (currentTitle) {
      let description = currentContent.join('\n').trim();
      
      // Handle token pairs replacement
      if (currentTitle === 'Token Pair Selection' && tokenPairs && tokenPairs.length > 0) {
        const tokenString = tokenPairs.join(', ');
        description = description.replace(/\{\{TOKEN_PAIRS\}\}/g, tokenString);
      }
      
      sections.push({ title: currentTitle, description });
    }
  }

  // Find matching section with case-insensitive comparison
  const matchedSection = sections.find(
    section => section.title.toLowerCase() === titleQuery.toLowerCase()
  ) || null;

  const titles = sections.map(section => section.title);

  return {
    matchedSection,
    allSections: sections,
    titles,
  };
}
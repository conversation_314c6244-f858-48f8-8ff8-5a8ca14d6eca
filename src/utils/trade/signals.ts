import { EMA, ATR, MACD, RSI } from 'technicalindicators';

export interface OHLCV {
  open: number[];
  high: number[];
  low: number[];
  close: number[];
  volume: number[];
}

export interface SignalResult {
  emaFast: number | null;
  emaSlow: number | null;
  atrValue: number | null;
  adxValue: number | null;
  macdValue: number | null;
  rsiValue: number | null;
  macdHist: number | null;
  macdHistPrev: number | null;
  marketRegime: string;
  signalScore?: number;
  rsiTrend?: number[];
}

export interface IndicatorValues {
  rsi: number;
  macdHist: number;
  macdHistPrev: number;
  emaSlope: number;
  adx: number;
  atrPct: number;
  atr: number;
  thresholds: Thresholds;
  rsiTrend: number[];
  volumePercent?: number;
}

export interface Thresholds {
  RSI: number;
  MACD_Histogram: number;
  EMA_Slope: number;
  ATRPercent: number;
  VolumePercent: number;
}

export class TradeMemory {
  private cooldownCounter: number;
  private lastResult: 'win' | 'loss' | null;

  constructor() {
    this.cooldownCounter = 0;
    this.lastResult = null;
  }

  recordTradeResult(outcome: 'win' | 'loss') {
    this.lastResult = outcome;
    this.cooldownCounter = outcome === 'loss' ? 5 : 0;
  }

  tick() {
    if (this.cooldownCounter > 0) this.cooldownCounter--;
  }

  canTrade(signalStrength: number, overrideThreshold = 0.8): boolean {
    if (this.cooldownCounter > 0 && signalStrength < overrideThreshold) return false;
    return true;
  }
}

export function isUncertainRegime(regime: string): boolean {
  return ['flat_or_choppy', 'volatile_uncertain', 'neutral'].includes(regime);
}

export function calculateSignalScore(
  indicators: IndicatorValues,
  thresholds: Thresholds
): number {
  let score = 0;
  let total = Object.keys(thresholds).length;

  if (indicators.rsi !== undefined) {
    if (indicators.rsi < 30 || indicators.rsi > 70) score++;
  }

  if (
    indicators.macdHist !== undefined &&
    Math.abs(indicators.macdHist) > thresholds.MACD_Histogram
  ) {
    score++;
  }

  if (
    indicators.emaSlope !== undefined &&
    Math.abs(indicators.emaSlope) > thresholds.EMA_Slope
  ) {
    score++;
  }

  if (
    indicators.atrPct !== undefined &&
    indicators.atrPct > thresholds.ATRPercent
  ) {
    score++;
  }

  if (
    indicators.volumePercent !== undefined &&
    indicators.volumePercent > thresholds.VolumePercent
  ) {
    score++;
  }

  return score / total;
}

export function buildIndicatorInputs(signalResult: SignalResult, closePrice: number): IndicatorValues {
  const slope =
    signalResult.emaFast && signalResult.emaSlow
      ? (signalResult.emaFast - signalResult.emaSlow) / signalResult.emaSlow
      : 0;

  const atrPct = signalResult.atrValue && closePrice
    ? signalResult.atrValue / closePrice
    : 0;

  const thresholds: Thresholds = {
    RSI: 0,
    MACD_Histogram: 0.001,
    EMA_Slope: 0.003,
    ATRPercent: 0.25,
    VolumePercent: 0.5
  };

  return {
    rsi: signalResult.rsiValue ?? 50,
    macdHist: signalResult.macdHist ?? 0,
    macdHistPrev: signalResult.macdHistPrev ?? 0,
    emaSlope: slope,
    adx: signalResult.adxValue ?? 20,
    atrPct,
    atr: signalResult.atrValue ?? 0,
    thresholds,
    rsiTrend: signalResult.rsiTrend ?? [],
    volumePercent: 0 // optional
  };
}

export function detectMarketRegime({
  emaShort,
  emaLong,
  rsi,
  macd,
  atrPct,
}: {
  emaShort: number;
  emaLong: number;
  rsi: number;
  macd: number;
  atrPct: number;
}):
  | 'extremely_bullish'
  | 'bullish'
  | 'range_bound'
  | 'flat_or_choppy'
  | 'bearish'
  | 'extremely_bearish'
  | 'volatile_uncertain'
  | 'neutral' {
  const emaGap = (emaShort - emaLong) / emaLong;
  const macdAbs = Math.abs(macd);

  if (emaGap > 0.008 && rsi > 75 && macd > 0 && atrPct > 0.4) {
    return 'extremely_bullish';
  }
  if (emaGap > 0.003 && rsi > 60 && macd > 0) {
    return 'bullish';
  }
  if (emaGap < -0.008 && rsi < 25 && macd < 0 && atrPct > 0.4) {
    return 'extremely_bearish';
  }
  if (emaGap < -0.003 && rsi < 40 && macd < 0) {
    return 'bearish';
  }
  if (Math.abs(emaGap) < 0.001 && rsi >= 45 && rsi <= 55 && atrPct < 0.2) {
    return 'flat_or_choppy';
  }
  if (atrPct > 0.5 && (rsi < 45 || rsi > 55) && macdAbs < 0.001) {
    return 'volatile_uncertain';
  }
  if (Math.abs(emaGap) < 0.002 && macdAbs < 0.001 && rsi > 48 && rsi < 52) {
    return 'range_bound';
  }
  return 'neutral';
}

export function checkSignals(symbol: string, ohlcv: OHLCV): SignalResult {
  const { open, high, low, close } = ohlcv;

  if (close.length < 26) {
    console.log(`⚠️ Not enough data for ${symbol} (need 26+ candles, got ${close.length})`);
    return {
      emaFast: null,
      emaSlow: null,
      atrValue: null,
      adxValue: null,
      macdValue: null,
      rsiValue: null,
      macdHist: null,
      macdHistPrev: null,
      marketRegime: 'neutral',
    };
  }

  const emaFastArr = EMA.calculate({ period: 5, values: close });
  const emaSlowArr = EMA.calculate({ period: 20, values: close });
  const atrArr = ATR.calculate({ period: 14, high, low, close });
  const macdArr = MACD.calculate({
    values: close,
    fastPeriod: 12,
    slowPeriod: 26,
    signalPeriod: 9,
    SimpleMAOscillator: false,
    SimpleMASignal: false,
  });
  const rsiArr = RSI.calculate({ period: 14, values: close });

  const emaFast = emaFastArr.at(-1) ?? null;
  const emaSlow = emaSlowArr.at(-1) ?? null;
  const atrValue = atrArr.at(-1) ?? null;
  const macdResult = macdArr.at(-1);
  const macdPrev = macdArr.at(-2);
  const macdValue = macdResult?.MACD ?? null;
  const rsiValue = rsiArr.at(-1) ?? null;
  const atrPct = atrValue && close.length > 0 ? atrValue / close.at(-1)! : 0;

  const marketRegime = (emaFast && emaSlow && rsiValue !== null && macdValue !== null)
    ? detectMarketRegime({ emaShort: emaFast, emaLong: emaSlow, rsi: rsiValue, macd: macdValue, atrPct })
    : 'neutral';

  const indicatorInputs = buildIndicatorInputs({
    emaFast,
    emaSlow,
    atrValue,
    adxValue: null,
    macdValue,
    rsiValue,
    macdHist: macdResult?.histogram ?? null,
    macdHistPrev: macdPrev?.histogram ?? null,
    marketRegime,
  }, close.at(-1) ?? 0);

  const thresholds: Thresholds = {
    RSI: 0,
    MACD_Histogram: 0.001,
    EMA_Slope: 0.003,
    ATRPercent: 0.25,
    VolumePercent: 0.5,
  };

  const signalScore = calculateSignalScore(indicatorInputs, thresholds);

  console.log(`📈 ${symbol} indicators:`);
  console.log(`  EMA(5): ${emaFast?.toFixed(6)}, EMA(20): ${emaSlow?.toFixed(6)}`);
  console.log(`  ATR(14): ${atrValue?.toFixed(6)}, ATR%: ${(atrPct * 100).toFixed(2)}%`);
  console.log(`  MACD(12,26): ${macdValue?.toFixed(6)}, RSI(14): ${rsiValue?.toFixed(2)}`);
  console.log(`  📊 Market Regime: ${marketRegime}, Signal Score: ${signalScore.toFixed(2)}`);

  return {
    emaFast,
    emaSlow,
    atrValue,
    adxValue: null,
    macdValue,
    macdHist: macdResult?.histogram ?? null,
    macdHistPrev: macdPrev?.histogram ?? null,
    rsiValue,
    marketRegime,
    signalScore,
    rsiTrend: rsiArr.slice(-20)
  };
}
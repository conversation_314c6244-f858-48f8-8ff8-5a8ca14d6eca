// improvementChecklist.ts — Enhancements to Reach 70% Win Rate

/**
 * ✅ ENFORCE STRONG SIGNAL SCORE GATE
 */
export const MIN_SIGNAL_SCORE = 0.4;
export const STRONG_SIGNAL_SCORE = 0.8;

/**
 * ✅ DYNAMIC TRADE ENTRY GATING
 */
export function shouldEnterTrade(passedChecks: number, requiredPasses: number, signalScore: number): boolean {
  if (signalScore < MIN_SIGNAL_SCORE) return false;
  return signalScore >= STRONG_SIGNAL_SCORE || passedChecks >= requiredPasses;
}

/**
 * ✅ COOLDOWN OVERRIDE ON STRONG SIGNALS
 */
export function canOverrideCooldown(signalScore: number, regime: string): boolean {
  return signalScore >= STRONG_SIGNAL_SCORE && ["bullish", "bearish"].includes(regime);
}

/**
 * ✅ POSITION SIZING BASED ON CONFIDENCE
 */
export function calculatePositionSize(budget: number, signalScore: number): number {
  if (signalScore >= 0.9) return budget * 1.2;
  if (signalScore >= 0.75) return budget * 1.0;
  return budget * 0.7;
}

/**
 * ✅ ADAPTIVE TP/SL BY SIGNAL STRENGTH
 */
export function getTP_SL_Ratio(signalScore: number): { tp: number; sl: number } {
  if (signalScore > 0.85) return { tp: 2.0, sl: 1.0 };
  if (signalScore > 0.7) return { tp: 1.5, sl: 1.0 };
  return { tp: 1.2, sl: 1.0 };
}

/**
 * ✅ AVOID LOW VOLATILITY ZONES
 */
export function isLowVolatility(atrPercent: number, recentATRTrend: number[]): boolean {
  const flat = recentATRTrend.every((v) => Math.abs(v - recentATRTrend[0]) < 0.0001);
  return atrPercent < 0.25 && flat;
}

/**
 * ✅ ENFORCE LOSS-BASED COOLDOWN STRATEGY
 */
export function shouldEnforceCooldown(lossStreak: number, lastPnL: number): boolean {
  return lastPnL <= -0.95 || lossStreak >= 2;
}

/**
 * ✅ DIVERGENCE FILTER PLACEHOLDER
 */
export function hasRSIDivergence(rsiTrend: number[], priceTrend: number[]): boolean {
  const rsiRising = rsiTrend[rsiTrend.length - 1] > rsiTrend[0];
  const priceFalling = priceTrend[priceTrend.length - 1] < priceTrend[0];
  return rsiRising && priceFalling;
}

/**
 * ✅ HYBRID EXIT STRATEGY LOGIC (TP, SL, Trailing, Breakeven)
 */
export function getExitStrategy(signalScore: number): "tp" | "sl" | "trailing" | "breakeven" {
  if (signalScore > 0.85) return "trailing";
  if (signalScore > 0.75) return "tp";
  return "breakeven";
}

/**
 * ✅ DEBUG REASON LOGGING (UPDATED)
 */
export function logTradeDecision(reason: string, details: Record<string, any>) {
  console.log(`🧠 Trade Decision Reason: ${reason}`);
  console.table(details);
}
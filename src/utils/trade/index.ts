import dotenv from 'dotenv';
import {
  initBlockchain,
  getTotalPnL,
  closeAllPositions,
  getPositions,
  getTradingHistory,
  runSignalCheckAndOpen,
  closePosition,
  priceFeeds,
} from './blockchain';
import { checkAndCloseForTP } from './strategyEngine';
import * as tradeService from '@/services/trade';
import * as agentService from '@/services/agent';
import * as chatService from '@/services/chat';
import decryptPrivateKey from '@/utils/blockchain/cryptography/decryptPrivateKey';
import { POSITION_STATUS, TRADE_STATUS } from '@/db/schemas/constants/trade';
import getTrendingTokens from './getTrendingTokens';

dotenv.config();

// Global variable to track currently running trades
const runningTrades = new Set<number>();

export function calculateLeverage(budget: number, profitGoal: number): number {
  const ROI = profitGoal / budget;

  // Linear equation constants
  const slope = 14.2697;
  const intercept = 1.5730;

  // Raw leverage calculation
  const rawLeverage = slope * ROI + intercept;

  // Clamp to platform bounds: [3, 130]
  const leverage = Math.min(130, Math.max(15, rawLeverage));

  return leverage;
}

async function updateOpenPositions(trade: any, agent: any, client: any, account: any) {
  const openPositions = await tradeService.getPositionsByTradeId(trade.id, POSITION_STATUS.OPEN);
  const rawPositions = await getPositions({ client, account });
  const currentOpen = new Set(
    rawPositions.filter(p => BigInt(p.size) !== 0n).map(p =>
      Object.keys(priceFeeds).find(sym => p.pairType.toUpperCase().includes(sym))!
    )
  );
  const history = await getTradingHistory({ client, account });
  for (const position of openPositions) {
    if (position.pair && !currentOpen.has(position.pair)) {
      const pairState = history.find(p => p.pairType.includes(position.pair) && ['position_liquidate', 'position_close'].includes(p.eventType));
      await tradeService.updatePosition(position.id, {
        status: pairState?.eventType === 'position_liquidate' ? POSITION_STATUS.LIQUIDATED : POSITION_STATUS.CLOSED,
        pnl: pairState?.pnlWithoutFee ?? 0,
        exitPrice: pairState?.price,
      });
      await chatService.addTradeChat({
        conversationId: trade.conversationId!,
        message: `Position for ${position.pair} ${pairState?.eventType === 'position_liquidate' ? 'liquidated' : 'closed' }. PnL: $${pairState?.pnlWithoutFee ?? 0}`,
        userId: agent.userId,
        agentId: agent.id,
        data: {
          tradeId: trade.id,
          pair: position.pair,
          pnl: pairState?.pnlWithoutFee ?? 0,
          exitPrice: pairState?.price,
          eventType: pairState?.eventType,
        }
      });
    }
  }
  const positions = await tradeService.getPositionsByTradeId(trade.id);
  const newOpenPositions = positions.filter(p => p.status === POSITION_STATUS.OPEN);
  const closedPositions = positions.filter(p => p.status !== POSITION_STATUS.OPEN);

  if (closedPositions.length && !newOpenPositions.length) {
    const totalAcquiredPnl = await tradeService.getTotalPnlPerTrade(trade.id);
    await tradeService.updateTrade(trade.id, {
      status: TRADE_STATUS.CLOSED,
      totalPnl: totalAcquiredPnl.toString(),
    });
    console.log(`❌ No open positions. Closing trade ${trade.id}`);
    await tradeService.createTradeLog({
      tradeId: trade.id,
      text: `❌ No open positions. Closing trade ${trade.id}`,
      logType: 'error',
    });
    await chatService.addTradeChat({
      conversationId: trade.conversationId!,
      message: `Trade successfully completed with. Total PnL: $${totalAcquiredPnl.toString(2)}`,
      userId: agent.userId,
      agentId: agent.id,
      data: {
        tradeId: trade.id,
        totalPnl: totalAcquiredPnl,
        isSuccess: totalAcquiredPnl > 0,
      }
    });
    await tradeService.createTradeLog({
      tradeId: trade.id,
      text: `Trade successfully completed with. Total PnL: $${totalAcquiredPnl.toFixed(2)}`,
      logType: totalAcquiredPnl > 0 ? 'success' : 'error',
    })
    return { isClosed: true };
  }
  return { isClosed: false };
}

async function handleTrade(tradeId: number) {
  // Check if trade is already running
  
  if (runningTrades.has(tradeId)) {
    console.log(`⏭️ Trade ${tradeId} is already running, skipping...`);
    await tradeService.createTradeLog({
      tradeId,
      text: `⏭️ Trade ${tradeId} is already running, skipping...`,
      logType: 'success',
    });
    return;
  }

  // Add trade to running trades
  runningTrades.add(tradeId);
  console.log(`🔄 Running trade ${tradeId}...`);
  await tradeService.createTradeLog({
    tradeId,
    text: `🔄 ######## Starting trade ${tradeId} ########`,
    logType: 'success',
  });

  try {
    const trade = await tradeService.getTradeById(tradeId);
    if (!trade) {
      console.error(`Trade with ID ${tradeId} not found.`);
      runningTrades.delete(tradeId); // Remove from running trades
      await tradeService.createTradeLog({
        tradeId,
        text: `❌ Trade with ID ${tradeId} not found.`,
        logType: 'error',
      });
      return;
    }

    const agent = await agentService.getFullAgentById(trade?.agentId ?? 0);
    if (!agent) {
      console.error(`Agent with ID ${trade?.agentId} not found.`);
      runningTrades.delete(tradeId); // Remove from running trades
      await tradeService.createTradeLog({
        tradeId,
        text: `❌ Agent with ID ${trade?.agentId} not found.`,
        logType: 'error',
      });
      return;
    }

    if (!agent.privateKey) {
      console.error(`Agent with ID ${trade?.agentId} has no private key.`);
      runningTrades.delete(tradeId); // Remove from running trades
      await tradeService.createTradeLog({
        tradeId,
        text: `❌ Agent with ID ${trade?.agentId} has no private key.`,
        logType: 'error',
      });
      return;
    }

    let openPositions = await tradeService.getPositionsByTradeId(tradeId, POSITION_STATUS.OPEN);

    const encryptedPrivateKey = decryptPrivateKey(agent.privateKey);

    const { client, aptos, account } = await initBlockchain(encryptedPrivateKey);

    const budget = trade?.budget ?? 50;
    const profitGoal = trade?.profitGoal ?? 10;
    const maxPositions = budget > 100 ? 6 : budget > 50 ? 4 : 2;
    const perPositionBudget = budget / maxPositions;

    let totalPnl = 0;
    let positionsOpened = openPositions.length;

    totalPnl = await getTotalPnL(client, account, tradeId);

    console.log(`💰 Total PnL: $${totalPnl.toFixed(2)} / $${profitGoal}`);
    await tradeService.createTradeLog({
      tradeId,
      text: `💰 Total PnL: $${totalPnl.toFixed(2)} / $${profitGoal}`,
      logType: 'info',
    });

    const closed = await checkAndCloseForTP({ tradeId, client, aptos, account, closePosition, profitTarget: profitGoal });
    if (closed) {
      totalPnl = await getTotalPnL(client, account, tradeId);
    }

    const { isClosed } = await updateOpenPositions(trade, agent, client, account);
    if (isClosed) {
      runningTrades.delete(tradeId); // Remove from running trades
      return;
    }

    if (totalPnl >= profitGoal) {
      await closeAllPositions({ client, aptos, account });

      // sleep for 10 seconds to allow for all positions to close
      await new Promise(resolve => setTimeout(resolve, 10000));

      await updateOpenPositions(trade, agent, client, account);
      console.log(`🎯 Profit goal reached. Closing all.`);
      await tradeService.createTradeLog({
        tradeId,
        text: `🎯 Profit goal reached. Closing all.`,
        logType: 'success',
      });
      runningTrades.delete(tradeId); // Remove from running trades
      return;
    }

    console.log(`💼 Open positions: ${openPositions.length}`);
    await tradeService.createTradeLog({
      tradeId,
      text: `💼 Open positions: ${openPositions.length}`,
      logType: 'info',
    });

    const leverage = calculateLeverage(budget, profitGoal);

    if (!positionsOpened || positionsOpened < maxPositions) {

      const trendingTokens = await getTrendingTokens();
      const requiredTokens = trendingTokens.slice(0, maxPositions * 2);

      console.log(`✅ Tokens to watch: ${requiredTokens.join(', ')}`);
      await tradeService.createTradeLog({
        tradeId,
        text: `✅ Tokens to watch: ${requiredTokens.join(', ')}`,
        logType: 'info',
      });
    
      for (const symbol of requiredTokens) {
        const ifPositionExists = openPositions.find(p => p.pair === symbol);
        if (ifPositionExists) continue;

        const opened = await runSignalCheckAndOpen({
          tradeId,
          client,
          aptos,
          account,
          symbol,
          perPositionBudget,
          leverage
        });

        if (opened?.positionOpened) {
          await tradeService.createPosition({
            tradeId,
            pair: symbol,
            budget: perPositionBudget,
            entryPrice: opened.entryPrice,
            marketRegime: opened.marketRegime,
            direction: opened.direction,
            leverage: opened.leverage,
            signalScore: opened.signalScore,
            rsi: opened.rsi,
            macdHist: opened.macdHist,
            emaSlope: opened.emaSlope,
            atrPct: opened.atrPct,
            txHash: opened.txHash,
            status: 'open',
          });

          positionsOpened++;

          console.log(`🚀 Opened position for ${symbol}`);
          await tradeService.createTradeLog({
            tradeId,
            text: `🚀 Opened position for ${symbol}`,
            logType: 'info',
          });

          await chatService.addTradeChat({
            conversationId: trade.conversationId!,
            message: `🚀 Opened position for ${symbol} at $${opened.entryPrice.toFixed(2)} with leverage ${opened.leverage}`,
            userId: agent.userId,
            agentId: agent.id,
            data: {
              symbol,
              tradeId,
              entryPrice: opened.entryPrice,
              leverage: opened.leverage,
              collateral: perPositionBudget,
            }
          })

          if (positionsOpened >= maxPositions) break;
        }
      }
    }
    runningTrades.delete(tradeId);
  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    const stack = error instanceof Error ? error.stack : 'No stack trace available';

    console.error(`Error running trade ${tradeId}: ${message}\nStack: ${stack}`);

    await tradeService.createTradeLog({
      tradeId,
      text: `❌ Error running trade: ${message}\nStack Trace:\n${stack}`,
      logType: 'error',
    });
    runningTrades.delete(tradeId);
  }
}

export default handleTrade;

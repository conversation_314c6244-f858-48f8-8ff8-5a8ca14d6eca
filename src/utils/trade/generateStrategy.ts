import { generateStrategyResponse } from '@/utils/trade/chatgpt';
import parseStrategyResponse from './parseStrategyResponse';
import handleTrade from '@/utils/trade';
import getTrendingTokens from '@/utils/trade/getTrendingTokens';
import * as chatService from '@/services/chat';
import * as tradeService from '@/services/trade';
import { TRADE_STATUS } from '@/db/schemas/constants/trade';

export default async function generateStrategy({tradeId, budget, profitGoal, conversationId, userId, agentId}: {
  tradeId: number;
  budget: number;
  profitGoal: number;
  conversationId: number;
  userId: number;
  agentId: number;
}): Promise<void> {

  // Generate the strategy response
  const strategyResponse = await generateStrategyResponse({
    budget,
    profitTarget: profitGoal,
  });

  const titles = [
    'Trading With A Fixed Budget And Profit Goal',
    'Token Pair Selection',
    'Budget Division And Leverage',
    'Technical Indicator Analysis',
    'Strategy To Achieve Profit Goal'
  ];

  // Loop through titles and create chats
  for (let i = 0; i < titles.length; i++) {
    const title = titles[i];
    
    // Get trending tokens only for Token Pair Selection
    let tokenPairs: string[] | undefined;
    if (title === 'Token Pair Selection') {
      tokenPairs = await getTrendingTokens();
    }
    
    // Parse strategy response for this title
    const parsedResponse = parseStrategyResponse(
      strategyResponse,
      title,
      tokenPairs
    );
    
    const message = parsedResponse.matchedSection?.description || `No content found for ${title}`;

    // Create chat
    await chatService.createChat({
      conversationId: conversationId,
      senderId: agentId,
      receiverId: userId,
      message: message,
      messageType: 'trade_message',
      isAgent: true,
      data: {
        title,
        message,
        tradeId,
        tradeStrategy: true
      }
    });

    // Add 2 second delay between chat creations (except for the last chat)
    if (i < titles.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  await tradeService.updateTrade(tradeId, {
    status: TRADE_STATUS.OPEN,
  });

  handleTrade(tradeId);
}
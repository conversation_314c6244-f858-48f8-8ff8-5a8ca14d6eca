// strategyEngine.ts — unified trading logic with signal evaluation
import { MerkleClient } from '@merkletrade/ts-sdk';
import { Account, Aptos } from '@aptos-labs/ts-sdk';
import {
  priceFeeds,
  fetchPrice,
  resolveSymbolFromPairType
} from './blockchain';
import {
  TradeMemory,
  Thresholds,
  buildIndicatorInputs,
  calculateSignalScore,
  checkSignals,
} from '@/utils/trade/signals';
import { fetchOHLCVFromBinance } from '@/utils/trade/binanceHistorical';
import adaptiveConfig from '@/utils/trade/adaptiveConfig.json';
import { hasRSIDivergence } from '@/utils/trade/improvementChecklist';
import { getAIPOS, setAIPOS } from '@/utils/trade/aiStorage';
import { recordTrade } from '@/utils/trade/winRateTracker';
import { logTrade } from '@/utils/trade/tradeLogger';
import * as tradeService from '@/services/trade';

export type EntryDecision = {
  shouldOpen: boolean;
  reason: string;
  confidence: 'low' | 'medium' | 'high';
  isAnticipation?: boolean;
  forcedBias?: boolean;
  leverage?: number;
  marketRegime?: string;
  signalScore?: number;
  direction?: 'long' | 'short';
  entryType?: 'long' | 'short';
};

// TP/SL logic removed as per requirements

export function shouldOpenHighConfidence({
  rsi,
  macdHist,
  macdHistPrev,
  emaSlope,
  atrPct,
  adx,
  regime,
  divergence,
  signalScore,
  tradeId
}: {
  rsi: number;
  macdHist: number;
  macdHistPrev: number;
  emaSlope: number;
  atrPct: number;
  adx: number;
  regime: string;
  divergence: boolean;
  signalScore: number;
  tradeId: number;
}): EntryDecision {
  const macdMomentum = macdHist - macdHistPrev;
  const withinNeutralMACD = Math.abs(macdHist) <= 0.0025;
  const emaBullish = emaSlope >= 0.015;
  const emaBearish = emaSlope <= -0.015;
  const isExtremeBull = rsi >= 78 && emaBullish && macdHist > 0;
  const isExtremeBear = rsi <= 22 && emaBearish && macdHist < 0;
  const highVolatility = atrPct >= 0.002;

  console.log(`📊 [Check] Regime: ${regime}, RSI: ${rsi.toFixed(2)}, MACD: ${macdHist.toFixed(5)}, MACDΔ: ${macdMomentum.toFixed(5)}, EMA Slope: ${emaSlope.toFixed(5)}, ADX: ${adx}, ATR%: ${(atrPct * 100).toFixed(2)}%, Score: ${signalScore.toFixed(2)}`);
  tradeService.createTradeLog({
    tradeId,
    text: `📊 [Check] Regime: ${regime}, RSI: ${rsi.toFixed(2)}, MACD: ${macdHist.toFixed(5)}, MACDΔ: ${macdMomentum.toFixed(5)}, EMA Slope: ${emaSlope.toFixed(5)}, ADX: ${adx}, ATR%: ${(atrPct * 100).toFixed(2)}%, Score: ${signalScore.toFixed(2)}`,
    logType: 'check'
  })

  // 🔥 EXTREME BULLISH → LONG ONLY (unless confirmed reversal)
  if (isExtremeBull) {
    if (macdMomentum > 0 && highVolatility && adx >= 10) {
      return {
        shouldOpen: true,
        reason: 'Extreme bullish breakout (LONG only)',
        confidence: 'high',
        direction: 'long'
      };
    }
    if (
      macdMomentum <= 0 &&
      rsi > 80 &&
      emaSlope > 0 &&
      signalScore >= 0.4 &&
      divergence
    ) {
      return {
        shouldOpen: true,
        reason: 'Bearish reversal in extreme bullish regime',
        confidence: 'medium',
        direction: 'short'
      };
    }
    return {
      shouldOpen: false,
      reason: 'Extreme bullish: no valid continuation or reversal signal',
      confidence: 'low'
    };
  }

  // ❄️ EXTREME BEARISH → SHORT ONLY (unless confirmed reversal)
  if (isExtremeBear) {
    if (macdMomentum < 0 && highVolatility && adx >= 10) {
      return {
        shouldOpen: true,
        reason: 'Extreme bearish breakdown (SHORT only)',
        confidence: 'high',
        direction: 'short'
      };
    }
    if (
      macdMomentum >= 0 &&
      rsi < 20 &&
      emaSlope < 0 &&
      signalScore >= 0.4 &&
      divergence
    ) {
      return {
        shouldOpen: true,
        reason: 'Bullish reversal in extreme bearish regime',
        confidence: 'medium',
        direction: 'long'
      };
    }
    return {
      shouldOpen: false,
      reason: 'Extreme bearish: no valid continuation or reversal signal',
      confidence: 'low'
    };
  }

  // 📈 BULLISH TREND CONTINUATION
  if (
    regime === 'bullish' &&
    rsi >= 45 && rsi <= 70 &&
    macdHist >= -0.001 &&
    emaSlope >= 0.002 &&
    highVolatility &&
    adx >= 10
  ) {
    return {
      shouldOpen: true,
      reason: 'Bullish trend continuation',
      confidence: 'high',
      direction: 'long'
    };
  }

  // 📉 BEARISH TREND CONTINUATION
  if (
    regime === 'bearish' &&
    rsi <= 55 && rsi >= 30 &&
    macdHist <= 0.001 &&
    emaSlope <= -0.002 &&
    highVolatility &&
    adx >= 10
  ) {
    return {
      shouldOpen: true,
      reason: 'Bearish trend continuation',
      confidence: 'high',
      direction: 'short'
    };
  }

  // 🤝 NEUTRAL RANGE SETUP
  if (
    regime === 'neutral' &&
    highVolatility &&
    adx < 20 &&
    (divergence || withinNeutralMACD)
  ) {
    const direction = emaSlope >= 0 ? 'long' : 'short';
    return {
      shouldOpen: true,
      reason: 'Neutral range-bound setup',
      confidence: 'medium',
      direction
    };
  }

  // 🔁 Reversal setups
  if (
    regime === 'bearish' &&
    rsi > 35 && rsi < 50 &&
    macdMomentum >= -0.001 &&
    withinNeutralMACD &&
    emaSlope >= -0.002 &&
    signalScore >= 0.3 &&
    highVolatility
  ) {
    return {
      shouldOpen: true,
      reason: 'Anticipating bullish reversal in bearish regime',
      confidence: 'medium',
      direction: 'long'
    };
  }

  if (
    regime === 'bullish' &&
    rsi < 65 && rsi > 45 &&
    macdMomentum <= 0.001 &&
    withinNeutralMACD &&
    emaSlope <= 0.002 &&
    signalScore >= 0.3 &&
    highVolatility
  ) {
    return {
      shouldOpen: true,
      reason: 'Anticipating bearish reversal in bullish regime',
      confidence: 'medium',
      direction: 'short'
    };
  }

  // 📉 Divergence fallback
  if (divergence && signalScore >= 0.2 && highVolatility) {
    return {
      shouldOpen: true,
      reason: 'Divergence fallback',
      confidence: 'low',
      direction: emaSlope >= 0 ? 'long' : 'short'
    };
  }

  return {
    shouldOpen: false,
    reason: 'No valid condition met',
    confidence: 'low'
  };
}

// 🚀 Unified Evaluator (call this everywhere)
export async function evaluateSignalOnly({ symbol, tradeId }: { symbol: string, tradeId: number }): Promise<EntryDecision> {
  const ohlcv = await fetchOHLCVFromBinance({ symbol });
  const signalResult = checkSignals(symbol, ohlcv);

  const {
    rsiValue: rsi,
    macdHist,
    macdHistPrev,
    emaFast,
    emaSlow,
    atrValue: atr,
    marketRegime: regime,
    signalScore = 0
  } = signalResult;

  const emaSlope =
    emaFast !== null && emaSlow !== null ? (emaFast - emaSlow) / emaSlow : 0;
  const atrPct = atr && ohlcv.close.length > 0 ? atr / ohlcv.close.at(-1)! : 0;
  const adx = 10; // Placeholder until actual ADX calc
  const divergence = hasRSIDivergence(signalResult.rsiTrend ?? [], ohlcv.close);

  const openCheck: EntryDecision = shouldOpenHighConfidence({
    rsi: rsi ?? 0,
    macdHist: macdHist ?? 0,
    macdHistPrev: macdHistPrev ?? macdHist ?? 0,
    emaSlope,
    atrPct,
    adx,
    regime,
    divergence,
    signalScore,
    tradeId
  });

  if (openCheck.shouldOpen && openCheck.reason.toLowerCase().includes('fallback')) {
    console.log(`🚀 [Fallback Trigger] ${symbol} | Reason: ${openCheck.reason} | Score: ${signalScore.toFixed(2)} | ATR%: ${(atrPct * 100).toFixed(2)}%`);
    await tradeService.createTradeLog({
      tradeId,
      text: `Fallback Triggered: ${openCheck.reason} | Score: ${signalScore.toFixed(2)} | ATR%: ${(atrPct * 100).toFixed(2)}%`,
      logType: 'fallback'
    });
  }

  const shouldOpen = typeof openCheck === 'object' ? openCheck.shouldOpen : false;
  const isAnticipation = typeof openCheck === 'object' ? openCheck.isAnticipation ?? false : false;
  const reason = typeof openCheck === 'object' ? openCheck.reason : 'No strong signal';
  const confidence = typeof openCheck === 'object' ? openCheck.confidence : 'low';

  const entryType: 'long' | 'short' =
    (rsi ?? 0) > 70 || (macdHist ?? 0) < 0 ? 'short' : 'long';

  const leverage = Math.max(
    30,
    adaptiveConfig.leverageStrategy[regime as keyof typeof adaptiveConfig.leverageStrategy] ?? 30
  );

  // ────── LOGGING ──────
  const indicatorLog = `🔍 [Indicators] RSI: ${(rsi ?? 0).toFixed(2)} | MACD Hist: ${(macdHist ?? 0).toFixed(6)} | EMA Slope: ${emaSlope.toFixed(5)} | ATR%: ${(atrPct * 100).toFixed(2)}%`
  const decisionLog = `🧠 [Decision] ${symbol} | Open: ${shouldOpen} | Reason: ${reason} | Confidence: ${confidence}`
  const logs = [indicatorLog, decisionLog];
  console.log(indicatorLog);
  console.log(decisionLog);
  await tradeService.createMultipleLogs(
    logs.map(log => ({
      tradeId,
      text: log,
      logType: 'signal'
    }))
  )

  return {
    shouldOpen,
    isAnticipation,
    reason,
    confidence,
    leverage,
    marketRegime: regime,
    signalScore
  };
}

// Check for profit goal only
export async function checkAndCloseForTP({
  tradeId,
  client,
  aptos,
  account,
  closePosition,
  profitTarget
}: {
  tradeId: number;
  client: MerkleClient;
  aptos: Aptos;
  account: Account;
  closePosition: (
    pair: string,
    pos: any,
    client: MerkleClient,
    aptos: Aptos,
    account: Account,
    reason: string,
    price: number
  ) => Promise<void>;
  profitTarget: number;
}): Promise<boolean> {
  const address = account.accountAddress.toString();
  const positions = await client.getPositions({ address }) as any;
  let totalPnl = 0;
  let closedAny = false;

  for (const pos of positions) {
    if (BigInt(pos.size) === 0n) continue;

    const symbol = Object.keys(priceFeeds).find(sym => pos.pairType.includes(sym));
    if (!symbol) continue;

    const entry = Number(pos.avgPrice ?? pos.entryPrice ?? 0) / 1e10;
    if (!entry || isNaN(entry)) continue;

    const mark = await fetchPrice(symbol);
    const size = Number(pos.size) / 1e6;
    const isLong = pos.isLong ?? pos.side === 'long';
    const dir = isLong ? 1 : -1;

    const movePct = ((mark - entry) / entry) * dir;
    const pnlUsd = movePct * size;
    const pnlPct = movePct * 100;
    totalPnl += pnlUsd;

    const stored = await tradeService.getPositionByPairType({
      tradeId,
      pair: pos.pairType,
    });
    if (!stored) continue;

    const icon = pnlPct > 0 ? '🟢' : pnlPct < 0 ? '🔴' : '⚪';
    console.log(`${icon} ${symbol} | PnL ${pnlPct.toFixed(2)}%`);
    await tradeService.createTradeLog({
      tradeId,
      text: `${icon} ${symbol} | PnL ${pnlPct.toFixed(2)}%`,
      logType: 'pnl'
    });

    // Logging positions
    console.log(`⏸️ Holding ${symbol} | PnL ${pnlPct.toFixed(2)}%`);
    await tradeService.createTradeLog({
      tradeId,
      text: `⏸️ Holding ${symbol} | PnL ${pnlPct.toFixed(2)}%`,
      logType: 'pnl'
    });
  }

  console.log(`💰 Total PnL across positions: $${totalPnl.toFixed(4)}`);
  await tradeService.createTradeLog({
    tradeId,
    text: `💰 Total PnL across positions: $${totalPnl.toFixed(4)}`,
    logType: 'pnl'
  });
  return closedAny;
}
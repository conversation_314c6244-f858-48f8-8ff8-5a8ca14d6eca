import { OpenAI } from 'openai';

export default async (message: string): Promise<string> => {
  try {
    const updatedMessage = `
    ${message}

    IMPORTANT:
    The response should be in proper HTML5 format.
    `;

    const openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-search-preview',
      messages: [
        {
          role: 'user',
          content: updatedMessage,
        },
      ],
    });

    return completion.choices[0]?.message?.content || 'No results found';
  } catch (error) {
    console.error('Error in OpenAI search:', error);
    return `Error searching with OpenAI: ${error instanceof Error ? error.message : String(error)}`;
  }
};

import cron from 'node-cron';

import { PerformFrequencyTrades } from './tasks/transactions/trade';
import { openDCATrades } from './tasks/transactions/dca';
import { checkExecutableSnipeOrders } from './tasks/transactions/snipe';
import { checkAndRunTrades } from './tasks/transactions/checkAndRunTrades';

interface CronJobConfig {
  task: () => Promise<void>;
  schedule: string;
  enabled: boolean;
  name: string;
}

const jobState: Record<string, { isRunning: boolean }> = {};

// Executes the task only if it's not already running
function wrapWithNonOverlappingExecution(
  task: () => Promise<void>,
  name: string,
): () => Promise<void> {
  if (!jobState[name]) {
    jobState[name] = { isRunning: false };
  }

  const execute = async () => {
    if (jobState[name].isRunning) {
      console.log(`[${name}] Skipped: already running`);
      return;
    }

    jobState[name].isRunning = true;
    console.log(`[${name}] Started at ${new Date().toISOString()}`);

    try {
      await task();
    } catch (err) {
      console.error(`[${name}] Error during execution:`, err);
    } finally {
      jobState[name].isRunning = false;
      console.log(`[${name}] Finished at ${new Date().toISOString()}`);
    }
  };

  return execute;
}

// Main cron job configuration
export const cronJobs: CronJobConfig[] = [
  // {
  //   task: PerformFrequencyTrades,
  //   schedule: '*/3 * * * *',
  //   enabled: true,
  //   name: 'PerformFrequencyTrades',
  // },
  // {
  //   task: openDCATrades,
  //   schedule: '*/1 * * * *',
  //   enabled: true,
  //   name: 'openDCATrades',
  // },
  // {
  //   task: checkExecutableSnipeOrders,
  //   schedule: '*/2 * * * *',
  //   enabled: true,
  //   name: 'checkExecutableSnipeOrders',
  // },
  // {
  //   task: checkAndRunTrades,
  //   schedule: '*/1 * * * *',
  //   enabled: true,
  //   name: 'checkAndRunTrades',
  // },
];

export function initializeCronJobs(): void {
  cronJobs.forEach(job => {
    if (job.enabled) {
      cron.schedule(job.schedule, wrapWithNonOverlappingExecution(job.task, job.name));
      console.log(`Scheduled job: ${job.name}`);
    }
  });
}

// checkAndRunTrades()
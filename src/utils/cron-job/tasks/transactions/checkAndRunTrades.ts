import * as tradeService from '@/services/trade';
import * as agentService from '@/services/agent';
import handleTrade from '@/utils/trade/index';
import { closeAllPositions, initBlockchain } from '@/utils/trade/blockchain';
import decryptPrivateKey from '@/utils/blockchain/cryptography/decryptPrivateKey';

export async function checkAndRunTrades() {

  // handleTrade(195);

  const agents = await agentService.getAllAgents(1, 100)
  for (const agentInfo of agents.agents) {
    const agent = await agentService.getFullAgentById(agentInfo.id);
    if (!agent.privateKey) {
      console.warn(`Agent ${agentInfo.id} does not have a private key. Skipping.`);
      continue;
    }
    console.log(`############################ Processing Agent ID: ${agent.id} ############################`);
    const encryptedPrivateKey = decryptPrivateKey(agent.privateKey);
    const { client, aptos, account } = await initBlockchain(encryptedPrivateKey);
    await closeAllPositions({ client, aptos, account });
  }

  // const openTrades = await tradeService.getOpenTrades();
  // handleTrade(openTrades[0]?.id);
  // console.log(`🔄 Found ${openTrades.length} open trades...`);
  // for (const trade of openTrades) {
  //   try {
  //     handleTrade(trade.id);
  //   } catch (error) {
  //     console.log(error);
  //   }
  // }
}

export function getMinutesFromFrequency(
  frequency: '5m' | '1H' | '3H' | '6H' | '12H' | '1D' | '7D',
): number {
  const availableTimestamps = {
    '5m': 5,
    '1H': 60,
    '3H': 3 * 60,
    '6H': 6 * 60,
    '12H': 12 * 60,
    '1D': 24 * 60,
    '7D': 7 * 24 * 60,
  };
  return availableTimestamps[frequency];
}

export function floorTimeTo(millis: number, multiple_of: number = 5): Date {
  const date = new Date(millis);

  const minutes = date.getMinutes();
  const roundedMinutes = Math.floor(minutes / multiple_of) * multiple_of;

  date.setMinutes(roundedMinutes);
  date.setSeconds(0);
  date.setMilliseconds(0);

  return date;
}

export async function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

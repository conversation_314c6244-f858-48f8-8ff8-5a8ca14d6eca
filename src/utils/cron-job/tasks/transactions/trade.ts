import { getTradesToOpen, setUpdatedAtToNow } from '@/db/indexer/trade';
import * as agentService from '@/services/agent';
import decryptPrivateKey from '@/utils/blockchain/cryptography/decryptPrivateKey';
import { openMarketOrder } from '@/utils/blockchain/transaction/tradeTransactions';
import { getMinutesFromFrequency } from './utils';

export async function PerformFrequencyTrades() {
  try {
    const tradesToOpen = await getTradesToOpen();
    for (const trade of tradesToOpen) {
      const { frequency, pair, is_long, collateral, leverage, agent_address, updated_at } = trade;
      const agent = await agentService.getAgentByPublicKey(agent_address);
      if (!agent) {
        throw new Error('Agent not found');
      }
      const privateKey = agent.privateKey;
      if (!privateKey) {
        throw new Error(`Agent wallet not found for ${agent_address}`);
      }

      const currentTime = new Date();
      const diffMs = currentTime.getTime() - updated_at.getTime();
      const diffSeconds = Math.floor(diffMs / 1000);
      const minutes = Math.ceil(diffSeconds / 60);

      let tradeRes;
      if (minutes >= getMinutesFromFrequency(frequency)) {
        const decryptedPrivateKey = decryptPrivateKey(privateKey);
        tradeRes = await openMarketOrder(decryptedPrivateKey, pair, leverage, collateral, is_long);
      }
      if (tradeRes?.success) {
        await setUpdatedAtToNow(agent_address, pair, is_long, frequency);
      }
    }
  } catch (error: Error | any) {
    throw new Error(error.message || 'Error opening limit order');
  }
}

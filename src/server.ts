if (process.env.NODE_ENV === 'production') {
  require('module-alias/register'); // Use module-alias only in production
}

import app from './app';
import dotenv from 'dotenv';

dotenv.config();

const PORT = process.env.PORT || 3000;

app
  .listen(PORT, () => {
    console.log(`Server is running on http://localhost:${PORT}`);
  })
  .on('error', err => {
    console.error('Failed to start server:', err);
  });

import db from '@/config/db';
import { agents, users } from '@/db/schemas';
import { eq, desc, sql } from 'drizzle-orm';
import { getTableColumns } from 'drizzle-orm';
import { ISafeAgent } from '@/db/schemas';

// Helper function to exclude specific fields from the agents table
const getAgentColumns = () => {
  const { privateKey, ...rest } = getTableColumns(agents);
  return rest;
};

// Fetch all agents (exclude specified fields)
export const getAllAgents = async (page: number, pageSize: number) => {
  const agentColumns = getAgentColumns();

  // Calculate offset
  const offset = (page - 1) * pageSize;

  // Get total count (for pagination metadata)
  const countResult = await db.select({ count: sql`count(*)` }).from(agents);

  const total = Number(countResult[0]?.count || 0);

  // Get paginated results
  const agentsList = await db
    .select({
      ...agentColumns,
    })
    .from(agents)
    .orderBy(desc(agents.createdAt))
    .limit(pageSize)
    .offset(offset);

  return {
    agents: agentsList,
    total,
  };
};

// Fetch an agent by ID (exclude specified fields)
export const getAgentById = async (id: number) => {
  const agentColumns = getAgentColumns();
  const agent = await db
    .select({
      ...agentColumns,
      remaimingPrompts: users.remainingPrompts,
    })
    .from(agents)
    .leftJoin(users, eq(agents.userId, users.id))
    .where(eq(agents.id, id))
    .limit(1);
  return agent[0] || null;
};

export const getAgentDetail = async (id: number) => {
  const agent = await db.select().from(agents).where(eq(agents.id, id)).limit(1);
  return agent[0] || null;
};

// Get Agent by User ID (exclude specified fields)
export const getAgentByUserId = async (userId: number) => {
  const agentColumns = getAgentColumns();
  const agent = await db
    .select({
      ...agentColumns,
      remaimingPrompts: users.remainingPrompts,
    })
    .from(agents)
    .leftJoin(users, eq(agents.userId, users.id))
    .where(eq(agents.userId, userId))
    .limit(1);
  return agent[0] || null;
};

export const getAgentByAgentId = async (agentId: number) => {
  const agentColumns = getAgentColumns();
  const agent = await db
    .select({
      ...agentColumns,
      remaimingPrompts: users.remainingPrompts,
    })
    .from(agents)
    .leftJoin(users, eq(agents.userId, users.id))
    .where(eq(agents.id, agentId))
    .limit(1);
  return agent[0] || null;
};

export const getAgentByPublicKey = async (publicKey: string) => {
  const agent = await db.select().from(agents).where(eq(agents.publicKey, publicKey)).limit(1);
  return agent[0] || null;
};

// Create a new agent (exclude specified fields in the returned result)
export const createAgent = async (data: any): Promise<Partial<ISafeAgent>> => {
  const result = await db.insert(agents).values(data).returning();
  const agentColumns = getAgentColumns();
  return Object.fromEntries(
    Object.entries(result[0]).filter(([key]) => key in agentColumns),
  ) as Partial<ISafeAgent>;
};


export const getFullAgentById = async (id: number) => {
  const agent = await db
    .select()
    .from(agents)
    .where(eq(agents.id, id))
    .limit(1);
  return agent[0] || null;
}
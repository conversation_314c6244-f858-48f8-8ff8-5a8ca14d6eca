import db from '@/config/db';
import { users, agents, purchases } from '@/db/schemas';
import { eq } from 'drizzle-orm';
import { getTableColumns } from 'drizzle-orm';

// Helper function to exclude specific fields from the agents table
const getAgentColumns = () => {
  const { privateKey, ...rest } = getTableColumns(agents);
  const booleanColumns = Object.keys(rest).reduce(
    (acc, key) => {
      acc[key] = true;
      return acc;
    },
    {} as Record<string, boolean>,
  );
  return booleanColumns;
};

// Fetch all users
export async function getAllUsers() {
  return await db.select().from(users);
}

// Fetch a user by ID
export async function getUserById(id: number) {
  return await db.query.users.findFirst({
    where: eq(users.id, id),
    with: {
      agent: {
        columns: getAgentColumns(),
      },
    },
  });
}

// Fetch a user by address
export async function getUserByAddress(address: string) {
  const user = await db.select().from(users).where(eq(users.address, address)).limit(1);
  return user.length > 0 ? user[0] : null;
}

// Create a new user
export async function createUser(data: { address: string }) {
  const result = await db.insert(users).values(data).returning();
  return result[0];
}

// Update a user's nonce or address
export async function updateUser(id: number, data: Partial<{ address: string }>) {
  return await db.update(users).set(data).where(eq(users.id, id)).returning();
}

export async function updateUserTransaction(id: number, data: any) {
  return await db.update(users).set(data).where(eq(users.id, id)).returning();
}

export async function addNewPurchase(data: any) {
  return await db.insert(purchases).values(data).returning();
}

export async function getPurchaseByTransactionHash(transactionHash: string) {
  return await db.query.purchases.findFirst({
    where: eq(purchases.transactionHash, transactionHash),
    with: {
      user: true,
    },
  });
}

// Delete a user
export async function deleteUser(id: number) {
  return await db.delete(users).where(eq(users.id, id));
}

export async function updateRemainingPrompts(userId: number, remainingPrompts: number) {
  return await db.update(users).set({ remainingPrompts }).where(eq(users.id, userId));
}


import db from '@/config/db';
import { trades, positions, logs, ITrade, IPosition, ILog } from '@/db/schemas/trade';
import { eq, and, desc, count, sql } from 'drizzle-orm';
import { TRADE_STATUS, POSITION_STATUS } from '@/db/schemas/constants/trade';
import { chats, conversations } from '@/db/schemas/chat';

/**
 * Get trades by user ID with pagination
 */
export const getTradesByUserId = async (
  userId: number,
  page: number = 1,
  limit: number = 10,
): Promise<{
  trades: ITrade[];
  totalItems: number;
  totalPages: number;
  currentPage: number;
  itemsPerPage: number;
}> => {
  const offset = (page - 1) * limit;

  // Fetch the trades
  const tradeResults = await db
    .select()
    .from(trades)
    .where(eq(trades.userId, userId))
    .orderBy(desc(trades.createdAt))
    .limit(limit)
    .offset(offset);

  // Fetch the total number of trades for the user
  const totalItems = await db
    .select({ count: count() })
    .from(trades)
    .where(eq(trades.userId, userId))
    .then(res => res[0]?.count ?? 0);

  // Calculate the total number of pages
  const totalPages = Math.ceil(totalItems / limit);

  // Fetch positions for each trade
  const tradesWithPositions = await Promise.all(
    tradeResults.map(async (trade) => {
      const positionsList = await db
        .select()
        .from(positions)
        .where(eq(positions.tradeId, trade.id));

      return {
        ...trade,
        positions: positionsList
      };
    })
  );

  return {
    trades: tradesWithPositions as ITrade[],
    totalItems,
    totalPages,
    currentPage: page,
    itemsPerPage: limit,
  };
};

/**
 * Get the latest trade for a user
 */
export const getLatestTradeForUser = async (userId: number): Promise<ITrade | null> => {
  const trade = await db
    .select()
    .from(trades)
    .where(eq(trades.userId, userId))
    .orderBy(desc(trades.createdAt))
    .limit(1);

  if (!trade.length) return null;

  // Fetch positions for the trade
  const positionsList = await db
    .select()
    .from(positions)
    .where(eq(positions.tradeId, trade[0].id));

  return {
    ...trade[0],
    positions: positionsList
  } as ITrade;
};

/**
 * Get positions for a trade with optional status filter
 */
export const getPositionsByTradeId = async (
  tradeId: number,
  status?: string,
): Promise<IPosition[]> => {
  if (status) {
    return await db
      .select()
      .from(positions)
      .where(
        and(
          eq(positions.tradeId, tradeId),
          eq(positions.status, status)
        )
      )
      .orderBy(desc(positions.createdAt));
  }

  return await db
    .select()
    .from(positions)
    .where(eq(positions.tradeId, tradeId))
    .orderBy(desc(positions.createdAt));
};

/**
 * Create a new trade
 */
export const createTrade = async (data: {
  userId: number;
  agentId: number;
  prompt: string;
  budget: number;
  profitGoal: number;
  status: string;
  chatId?: number;
}): Promise<ITrade> => {
  const result = await db.insert(trades).values(data).returning();
  return result[0];
};

/**
 * Create a new position
 */
export const createPosition = async (data: {
  tradeId: number;
  pair: string;
  budget: number;
  status: string;
  marketRegime?: string;
  entryPrice?: number;
  direction?: string;
  leverage?: number;
  signalScore?: number;
  rsi?: number;
  macdHist?: number;
  emaSlope?: number;
  atrPct?: number;
  txHash?: string;
}): Promise<IPosition> => {
  // Convert numeric values to strings for database compatibility
  const formattedData = {
    ...data,
    budget: data.budget.toString(),
    entryPrice: data.entryPrice?.toString(),
    leverage: data.leverage?.toString(),
    signalScore: data.signalScore?.toString(),
    rsi: data.rsi?.toString(),
    macdHist: data.macdHist?.toString(),
    emaSlope: data.emaSlope?.toString(),
    atrPct: data.atrPct?.toString(),
  };

  const result = await db.insert(positions).values(formattedData).returning();
  return result[0];
};

/**
 * Create a log for a trade
 */
export const createTradeLog = async (data: {
  tradeId: number;
  text: string;
  logType: string;
}): Promise<ILog> => {
  const result = await db.insert(logs).values(data).returning();
  return result[0];
};

/**
 * Get all logs for a trade, ordered by timestamp (createdAt)
 * @param tradeId The ID of the trade
 * @param latestLast If true, orders logs with latest last (ascending), otherwise latest first (descending)
 */
export const getLogsByTradeId = async (
  tradeId: number,
  latestLast: boolean = true
): Promise<ILog[]> => {
  return await db
    .select()
    .from(logs)
    .where(eq(logs.tradeId, tradeId))
    .orderBy(latestLast ? logs.createdAt : desc(logs.createdAt));
};

/**
 * Get logs for a trade by type
 */
export const getLogsByTradeIdAndType = async (tradeId: number, logType: string): Promise<ILog[]> => {
  return await db
    .select()
    .from(logs)
    .where(and(eq(logs.tradeId, tradeId), eq(logs.logType, logType)))
    .orderBy(desc(logs.createdAt));
};

/**
 * Update a trade with optional fields
 */
export const updateTrade = async (
  id: number,
  data: Partial<Omit<ITrade, 'id' | 'createdAt' | 'updatedAt'>>,
): Promise<ITrade | null> => {
  const result = await db
    .update(trades)
    .set({
      ...data,
      updatedAt: sql`now()`
    })
    .where(eq(trades.id, id))
    .returning();

  return result[0] ?? null;
};

/**
 * Update a position with optional fields
 */
export const updatePosition = async (
  id: number,
  data: Partial<Omit<IPosition, 'id' | 'createdAt' | 'updatedAt'>>,
): Promise<IPosition | null> => {
  const result = await db
    .update(positions)
    .set({
      ...data,
      updatedAt: sql`now()`
    })
    .where(eq(positions.id, id))
    .returning();

  return result[0] ?? null;
};

/**
 * Get a trade by ID
 */
export const getTradeById = async (id: number): Promise<(ITrade & { conversationId: number | null, positions: IPosition[] }) | null> => {
  // Fetch trade with chat (to get conversationId)
  const tradeWithChat = await db
    .select({
      trade: trades,
      conversationId: chats.conversationId
    })
    .from(trades)
    .leftJoin(chats, eq(trades.chatId, chats.id))
    .where(eq(trades.id, id))
    .limit(1);

  if (!tradeWithChat.length) return null;

  const { trade, conversationId } = tradeWithChat[0];

  // Fetch positions for the trade
  const positionsList = await db
    .select()
    .from(positions)
    .where(eq(positions.tradeId, trade.id));

  return {
    ...trade,
    conversationId,
    positions: positionsList,
  };
};

/**
 * Get a position by ID
 */
export const getPositionById = async (id: number): Promise<IPosition | null> => {
  const position = await db
    .select()
    .from(positions)
    .where(eq(positions.id, id))
    .limit(1);

  return position[0] ?? null;
};

export const getPositionByPairType = async ({
  tradeId,
  pair,
}: {
  tradeId: number;
  pair: string;
}
): Promise<IPosition | null> => {
  const position = await db
    .select()
    .from(positions)
    .where(
      and(
        eq(positions.tradeId, tradeId),
        eq(positions.pair, pair),
        eq(positions.status, POSITION_STATUS.OPEN)
      )
    )
    .limit(1);

  return position[0] ?? null;
}

export const getTotalPnlPerTrade = async (tradeId: number): Promise<number> => {
  const result = await db
    .select({
      totalPnl: sql<number>`SUM(${positions.pnl})`.as('totalPnl')
    })
    .from(positions)
    .where(eq(positions.tradeId, tradeId))
    .then(res => res[0]?.totalPnl ?? 0);

  return result;
}

export const getOpenTrades = async (): Promise<ITrade[]> => {
  return await db
    .select()
    .from(trades)
    .where(eq(trades.status, TRADE_STATUS.OPEN));
}

export const checkIfOpenTradeExists = async (userId: number): Promise<boolean> => {
  const result = await db
    .select()
    .from(trades)
    .where(
      and(
        eq(trades.userId, userId),
        sql`${trades.status} = ${TRADE_STATUS.OPEN} OR ${trades.status} = ${TRADE_STATUS.PENDING}`
      )
    )
    .limit(1);

  return result.length > 0;
}

/**
 * Get a trade by ID and userId (for authorization)
 * @param tradeId The ID of the trade
 * @param userId The ID of the user
 * @returns The trade if it exists and belongs to the user, null otherwise
 */
export const getTradeByIdAndUserId = async (
  tradeId: number,
  userId: number
): Promise<ITrade | null> => {
  const trade = await db
    .select()
    .from(trades)
    .where(
      and(
        eq(trades.id, tradeId),
        eq(trades.userId, userId)
      )
    )
    .limit(1);

  if (!trade.length) return null;

  // Fetch positions for the trade
  const positionsList = await db
    .select()
    .from(positions)
    .where(eq(positions.tradeId, trade[0].id));

  return {
    ...trade[0],
    positions: positionsList
  } as ITrade;
};

/**
 * Get only the text fields for logs of a trade by tradeId
 * @param tradeId The ID of the trade
 * @param latestLast If true, orders logs with latest last (ascending), otherwise latest first (descending)
 */
export const getLogsByTradeIdOnly = async (
  tradeId: number,
  latestLast: boolean = true
): Promise<{ text: string }[]> => {
  return await db
    .select({ text: logs.text })
    .from(logs)
    .where(eq(logs.tradeId, tradeId))
    .orderBy(latestLast ? logs.createdAt : desc(logs.createdAt));
};

export const createMultipleLogs = async (
  logsData: Array<{ tradeId: number; text: string; logType: string; }>
): Promise<ILog[]> => {
  if (!logsData.length) return [];
  const result = await db
    .insert(logs)
    .values(logsData)
    .returning();
  return result;
};


/**
 * Get trade with chat and respective conversation as well with conversation isPublic flag
 * @param tradeId The ID of the trade
 * @returns trade with chat and conversation details
 */
export const getTradeWithChatAndConversation = async (
  tradeId: number
): Promise<(ITrade & { conversationId: number | null, isPublic: boolean | null }) | null> => {
  // Fetch trade with chat and conversation (to get conversationId and isPublic)
  const tradeWithChatAndConversation = await db
    .select({
      trade: trades,
      conversationId: chats.conversationId,
      isPublic: conversations.isPublic
    })
    .from(trades)
    .leftJoin(chats, eq(trades.chatId, chats.id))
    .leftJoin(conversations, eq(chats.conversationId, conversations.id))
    .where(eq(trades.id, tradeId))
    .limit(1);

  if (!tradeWithChatAndConversation.length) return null;

  const { trade, conversationId, isPublic } = tradeWithChatAndConversation[0];

  return {
    ...trade,
    conversationId,
    isPublic,
  };
}

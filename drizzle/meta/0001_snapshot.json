{"id": "cb140ca0-52f9-4781-86fd-7d8ed0248fe9", "prevId": "14c49cd6-3526-421c-aadd-c593757f6837", "version": "7", "dialect": "postgresql", "tables": {"public.agents": {"name": "agents", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "public_key": {"name": "public_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "private_key": {"name": "private_key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"agents_user_id_users_id_fk": {"name": "agents_user_id_users_id_fk", "tableFrom": "agents", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chats": {"name": "chats", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "conversation_id": {"name": "conversation_id", "type": "integer", "primaryKey": false, "notNull": true}, "sender_id": {"name": "sender_id", "type": "integer", "primaryKey": false, "notNull": true}, "receiver_id": {"name": "receiver_id", "type": "integer", "primaryKey": false, "notNull": true}, "is_agent": {"name": "is_agent", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "message_type": {"name": "message_type", "type": "text", "primaryKey": false, "notNull": true, "default": "'chit_chat'"}, "data": {"name": "data", "type": "json", "primaryKey": false, "notNull": false, "default": "'{}'::json"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"conversation_idx": {"name": "conversation_idx", "columns": [{"expression": "conversation_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sender_idx": {"name": "sender_idx", "columns": [{"expression": "sender_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "receiver_idx": {"name": "receiver_idx", "columns": [{"expression": "receiver_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chats_conversation_id_conversations_id_fk": {"name": "chats_conversation_id_conversations_id_fk", "tableFrom": "chats", "tableTo": "conversations", "columnsFrom": ["conversation_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.conversations": {"name": "conversations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"conversations_user_id_users_id_fk": {"name": "conversations_user_id_users_id_fk", "tableFrom": "conversations", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "conversations_agent_id_agents_id_fk": {"name": "conversations_agent_id_agents_id_fk", "tableFrom": "conversations", "tableTo": "agents", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.purchases": {"name": "purchases", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "transaction_hash": {"name": "transaction_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "amount_paid": {"name": "amount_paid", "type": "numeric(15, 2)", "primaryKey": false, "notNull": true}, "bought_credit": {"name": "bought_credit", "type": "numeric(15, 2)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"transaction_hash_idx": {"name": "transaction_hash_idx", "columns": [{"expression": "transaction_hash", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_id_idx": {"name": "user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"purchases_user_id_users_id_fk": {"name": "purchases_user_id_users_id_fk", "tableFrom": "purchases", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"purchases_transaction_hash_unique": {"name": "purchases_transaction_hash_unique", "nullsNotDistinct": false, "columns": ["transaction_hash"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "remaining_prompts": {"name": "remaining_prompts", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"address_idx": {"name": "address_idx", "columns": [{"expression": "address", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_address_unique": {"name": "users_address_unique", "nullsNotDistinct": false, "columns": ["address"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.positions": {"name": "positions", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "trade_id": {"name": "trade_id", "type": "integer", "primaryKey": false, "notNull": true}, "pair": {"name": "pair", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "budget": {"name": "budget", "type": "numeric(15, 4)", "primaryKey": false, "notNull": true}, "market_regime": {"name": "market_regime", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "entry_price": {"name": "entry_price", "type": "numeric(15, 8)", "primaryKey": false, "notNull": false}, "direction": {"name": "direction", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "leverage": {"name": "leverage", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false}, "tp": {"name": "tp", "type": "numeric(15, 8)", "primaryKey": false, "notNull": false}, "sl": {"name": "sl", "type": "numeric(15, 8)", "primaryKey": false, "notNull": false}, "signal_score": {"name": "signal_score", "type": "numeric(8, 4)", "primaryKey": false, "notNull": false}, "rsi": {"name": "rsi", "type": "numeric(8, 4)", "primaryKey": false, "notNull": false}, "macd_hist": {"name": "macd_hist", "type": "numeric(8, 4)", "primaryKey": false, "notNull": false}, "ema_slope": {"name": "ema_slope", "type": "numeric(8, 4)", "primaryKey": false, "notNull": false}, "atr_pct": {"name": "atr_pct", "type": "numeric(8, 4)", "primaryKey": false, "notNull": false}, "tx_hash": {"name": "tx_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"trade_id_idx": {"name": "trade_id_idx", "columns": [{"expression": "trade_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"positions_trade_id_trades_id_fk": {"name": "positions_trade_id_trades_id_fk", "tableFrom": "positions", "tableTo": "trades", "columnsFrom": ["trade_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.trades": {"name": "trades", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}, "agent_id": {"name": "agent_id", "type": "integer", "primaryKey": false, "notNull": true}, "chat_id": {"name": "chat_id", "type": "integer", "primaryKey": false, "notNull": false}, "prompt": {"name": "prompt", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": true}, "budget": {"name": "budget", "type": "integer", "primaryKey": false, "notNull": true}, "updated_budget": {"name": "updated_budget", "type": "integer", "primaryKey": false, "notNull": false}, "profit_goal": {"name": "profit_goal", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"trades_user_id_idx": {"name": "trades_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "trades_agent_id_idx": {"name": "trades_agent_id_idx", "columns": [{"expression": "agent_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "trades_chat_id_idx": {"name": "trades_chat_id_idx", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"trades_user_id_users_id_fk": {"name": "trades_user_id_users_id_fk", "tableFrom": "trades", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "trades_agent_id_agents_id_fk": {"name": "trades_agent_id_agents_id_fk", "tableFrom": "trades", "tableTo": "agents", "columnsFrom": ["agent_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "trades_chat_id_chats_id_fk": {"name": "trades_chat_id_chats_id_fk", "tableFrom": "trades", "tableTo": "chats", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}
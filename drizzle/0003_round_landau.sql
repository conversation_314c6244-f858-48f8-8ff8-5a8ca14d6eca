ALTER TABLE "positions" ADD COLUMN "exit_price" numeric(15, 8);--> statement-breakpoint
ALTER TABLE "positions" ADD COLUMN "pnl" numeric(15, 8);--> statement-breakpoint
ALTER TABLE "positions" ADD COLUMN "breakeven_activated" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "trades" ADD COLUMN "total_pnl" integer DEFAULT 0 NOT NULL;--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "positions_pair_idx" ON "positions" USING btree ("pair");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "positions_status_idx" ON "positions" USING btree ("status");